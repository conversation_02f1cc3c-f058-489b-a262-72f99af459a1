# Griffin Gadgets Inventory Management System

A comprehensive inventory management system for Griffin Gadgets, a retailer specializing in phones (iPhones and Samsung) and gaming products.

## Features

- **Dual User Roles**: Admin and Store Keeper interfaces
- **Inventory Management**: Add, update, and track products
- **Stock Tracking**: Monitor stock levels with low stock alerts
- **Sales Recording**: Record daily sales and generate reports
- **User Management**: Admin approval for new store keepers
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## System Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- XAMPP/WAMP/MAMP for local development

## Installation

1. Clone or download this repository to your web server's document root (e.g., `htdocs` for XAMPP)
2. Create a database named `griffin_inventory` in MySQL
3. Import the database schema by visiting `http://localhost/INVENTORY/setup.php` in your browser
4. Access the application at `http://localhost/INVENTORY`

## Default Login Credentials

### Admin
- Username: admin
- Password: admin@2023

### Store Keeper
- Store keepers need to register and be approved by an admin
- Default password after approval: 12345678

## User Management

- **Admin**: Can manage products, inventory, users, and generate reports
- **Store Keeper**: Can record sales, view inventory, and receive low stock alerts
- New store keepers must register and be approved by an admin
- Store keepers can request password resets which must be approved by an admin

## Directory Structure

- `/admin` - Admin interface files
- `/storekeeper` - Store Keeper interface files
- `/includes` - Common PHP includes (header, footer, functions)
- `/css` - Stylesheets
- `/js` - JavaScript files
- `/images` - Image assets
- `/database` - Database setup files

## Technologies Used

- PHP
- MySQL
- Bootstrap 5
- Font Awesome
- JavaScript/jQuery
- Chart.js (for reports)

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Author

Developed by Eberechukwu Charles Joseph for Griffin Gadgets.
