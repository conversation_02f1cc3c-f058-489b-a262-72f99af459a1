<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Get total products count
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
$total_products = $stmt->fetch()['total'];

// Get total stock value
$stmt = $pdo->query("SELECT SUM(price * quantity) as total_value FROM products");
$total_stock_value = $stmt->fetch()['total_value'];

// Get low stock products count
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE quantity <= alert_level");
$low_stock_count = $stmt->fetch()['total'];

// Get today's sales
$today = date('Y-m-d');
$stmt = $pdo->prepare("
    SELECT COUNT(t.transaction_id) as transaction_count, SUM(t.quantity) as total_quantity, SUM(t.quantity * p.price) as total_sales
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    WHERE t.transaction_type = 'out' AND DATE(t.transaction_date) = ?
");
$stmt->execute([$today]);
$today_sales_data = $stmt->fetch();
$today_sales = $today_sales_data['total_sales'] ?: 0;
$today_quantity = $today_sales_data['total_quantity'] ?: 0;
$today_transaction_count = $today_sales_data['transaction_count'] ?: 0;

// Get pending users count
$stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE status = 'pending'");
$pending_users_count = $stmt->fetch()['total'];

// Get password reset requests count
$stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE password_reset_requested = TRUE");
$password_reset_count = $stmt->fetch()['total'];

// Get recent transactions
$recent_transactions = getRecentTransactions($pdo, 5);

// Get low stock products
$low_stock_products = getLowStockProducts($pdo);

// Include header
include '../includes/header.php';
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Admin Dashboard</h1>
        <p class="text-muted">Welcome back, <?php echo $_SESSION['full_name']; ?></p>
    </div>
    <div class="col-md-6 text-md-end">
        <p class="mb-0">Today: <?php echo date('F d, Y'); ?></p>
    </div>
</div>

<!-- Dashboard Stats -->
<div class="row g-4 mb-4">
    <div class="col-md-6 col-lg-3" data-animate>
        <a href="products.php" class="text-decoration-none">
            <div class="card dashboard-card primary h-100">
                <div class="card-body">
                    <div class="icon"><i class="fas fa-boxes"></i></div>
                    <h5 class="card-title">Total Products</h5>
                    <p class="card-value"><?php echo $total_products; ?></p>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3" data-animate>
        <a href="stock-value.php" class="text-decoration-none">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="icon"><i class="fas fa-dollar-sign"></i></div>
                    <h5 class="card-title">Stock Value</h5>
                    <p class="card-value"><?php echo formatCurrency($total_stock_value); ?></p>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3" data-animate>
        <a href="alerts.php" class="text-decoration-none">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="icon"><i class="fas fa-exclamation-triangle"></i></div>
                    <h5 class="card-title">Low Stock Items</h5>
                    <p class="card-value"><?php echo $low_stock_count; ?></p>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3" data-animate>
        <a href="transactions.php" class="text-decoration-none">
            <div class="card dashboard-card danger h-100">
                <div class="card-body">
                    <div class="icon"><i class="fas fa-shopping-cart"></i></div>
                    <h5 class="card-title">Today's Sales</h5>
                    <p class="card-value"><?php echo $today_quantity; ?> items</p>
                    <p class="card-subtitle"><?php echo formatCurrency($today_sales); ?></p>
                </div>
            </div>
        </a>
    </div>
</div>

<!-- Stock Book Button -->
<div class="row mb-4">
    <div class="col-12" data-animate>
        <a href="stock-book.php" class="text-decoration-none">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="fas fa-book fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="mb-0">Stock Book Report</h5>
                            <p class="text-muted mb-0">View comprehensive stock movement report with opening and closing balances</p>
                        </div>
                        <div class="ms-auto">
                            <span class="btn btn-success">
                                <i class="fas fa-arrow-right"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>

<!-- User Management Stats -->
<?php if ($pending_users_count > 0 || $password_reset_count > 0): ?>
<div class="row g-4 mb-4">
    <?php if ($pending_users_count > 0): ?>
    <div class="col-md-6" data-animate>
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-warning text-dark d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-user-clock fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Pending User Approvals</h6>
                        <h4 class="mb-0"><?php echo $pending_users_count; ?> user<?php echo $pending_users_count > 1 ? 's' : ''; ?></h4>
                    </div>
                    <div class="ms-auto">
                        <a href="users.php" class="btn btn-warning">Review</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($password_reset_count > 0): ?>
    <div class="col-md-6" data-animate>
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-info text-dark d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-key fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Password Reset Requests</h6>
                        <h4 class="mb-0"><?php echo $password_reset_count; ?> request<?php echo $password_reset_count > 1 ? 's' : ''; ?></h4>
                    </div>
                    <div class="ms-auto">
                        <a href="users.php" class="btn btn-info">Review</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<div class="row g-4">
    <!-- Recent Transactions -->
    <div class="col-lg-8" data-animate>
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Transactions</h5>
                    <div>
                        <a href="stock-book.php" class="btn btn-sm btn-success me-2">
                            <i class="fas fa-book me-1"></i> Stock Book
                        </a>
                        <a href="users.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-users me-1"></i> Manage Users
                        </a>
                        <a href="transactions.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Type</th>
                                <th>Quantity</th>
                                <th>User</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($recent_transactions) > 0): ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($transaction['product_name']); ?></td>
                                        <td>
                                            <?php if ($transaction['transaction_type'] == 'in'): ?>
                                                <span class="badge bg-success">Stock In</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Stock Out</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $transaction['quantity']; ?></td>
                                        <td><?php echo htmlspecialchars($transaction['username']); ?></td>
                                        <td><?php echo date('M d, Y H:i', strtotime($transaction['transaction_date'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center">No recent transactions found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Alerts -->
    <div class="col-lg-4" data-animate>
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Low Stock Alerts</h5>
                    <a href="alerts.php" class="btn btn-sm btn-primary">View All</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($low_stock_products) > 0): ?>
                    <?php foreach ($low_stock_products as $product): ?>
                        <div class="alert-container mb-3">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center">
                                        <div class="alert-icon <?php echo $product['quantity'] <= 0 ? 'danger' : 'warning'; ?>">
                                            <i class="fas <?php echo $product['quantity'] <= 0 ? 'fa-times' : 'fa-exclamation'; ?>"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                            <div class="d-flex align-items-center mt-1">
                                                <span class="me-3">
                                                    <strong>Current:</strong> <?php echo $product['quantity']; ?>
                                                </span>
                                                <span>
                                                    <strong>Alert Level:</strong> <?php echo $product['alert_level']; ?>
                                                </span>
                                            </div>
                                            <div class="mt-2">
                                                <a href="stock-in.php?product_id=<?php echo $product['product_id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-plus-circle me-1"></i> Add Stock
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p>No low stock alerts at the moment.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Reset System Section -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>System Reset</h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5>Reset Inventory System</h5>
                        <p class="text-muted mb-0">This action will clear all products and sales data, creating a clean slate for the system. All products will be deleted and all transaction records will be removed. User accounts and system settings will be maintained.</p>
                    </div>
                    <div class="col-md-4 text-md-end mt-3 mt-md-0">
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#resetConfirmModal">
                            <i class="fas fa-trash-alt me-2"></i>Reset System
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Confirmation Modal -->
<div class="modal fade" id="resetConfirmModal" tabindex="-1" aria-labelledby="resetConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="resetConfirmModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>Confirm System Reset</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. All products will be deleted and all transaction records will be removed.
                </div>

                <p>Please enter your admin password to confirm this action:</p>

                <form id="resetForm" action="reset_system.php" method="post">
                    <div class="mb-3">
                        <label for="adminPassword" class="form-label">Admin Password</label>
                        <input type="password" class="form-control" id="adminPassword" name="admin_password" required>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmReset" name="confirm_reset" required>
                        <label class="form-check-label" for="confirmReset">
                            I understand that this will delete all products and remove all transaction records
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="document.getElementById('resetForm').submit();">
                    <i class="fas fa-trash-alt me-2"></i>Reset System
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include '../includes/footer.php';
?>
