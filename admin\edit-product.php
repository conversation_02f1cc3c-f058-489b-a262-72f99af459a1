<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Check if ID parameter exists
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: products.php?error=Invalid product ID");
    exit;
}

$product_id = $_GET['id'];

// Initialize variables
$product_name = $category = $description = $price = $quantity = $alert_level = $image_path = "";
$product_name_err = $category_err = $price_err = $quantity_err = $alert_level_err = $image_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate product name
    if (empty(trim($_POST["product_name"]))) {
        $product_name_err = "Please enter a product name.";
    } else {
        $product_name = trim($_POST["product_name"]);
    }

    // Validate category
    if (empty(trim($_POST["category"]))) {
        $category_err = "Please select a category.";
    } else {
        $category = trim($_POST["category"]);
    }

    // Validate price
    if (empty(trim($_POST["price"]))) {
        $price_err = "Please enter a price.";
    } elseif (!is_numeric($_POST["price"]) || $_POST["price"] <= 0) {
        $price_err = "Please enter a valid price.";
    } else {
        $price = trim($_POST["price"]);
    }

    // Validate alert level
    if (empty(trim($_POST["alert_level"]))) {
        $alert_level_err = "Please enter an alert level.";
    } elseif (!is_numeric($_POST["alert_level"]) || $_POST["alert_level"] < 0) {
        $alert_level_err = "Please enter a valid alert level.";
    } else {
        $alert_level = trim($_POST["alert_level"]);
    }

    // Get description and current image path
    $description = trim($_POST["description"]);
    $current_image = trim($_POST["current_image"]);

    // Handle image upload
    $image_path = $current_image;
    if (isset($_FILES["product_image"]) && $_FILES["product_image"]["error"] == 0) {
        $allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
        $max_size = 5 * 1024 * 1024; // 5MB

        if (!in_array($_FILES["product_image"]["type"], $allowed_types)) {
            $image_err = "Only JPG, JPEG, PNG, and GIF files are allowed.";
        } elseif ($_FILES["product_image"]["size"] > $max_size) {
            $image_err = "Image size should be less than 5MB.";
        } else {
            // Create directory if it doesn't exist
            $upload_dir = "../images/products/";
            if (!file_exists($upload_dir)) {
                if (!mkdir($upload_dir, 0777, true)) {
                    $image_err = "Failed to create upload directory. Please check permissions.";
                }
            }

            // Make sure the directory is writable
            if (empty($image_err) && !is_writable($upload_dir)) {
                chmod($upload_dir, 0777);
                if (!is_writable($upload_dir)) {
                    $image_err = "Upload directory is not writable. Please check permissions.";
                }
            }

            // Generate unique filename
            if (empty($image_err)) {
                // Keep original filename but make it unique
                $original_filename = pathinfo($_FILES["product_image"]["name"], PATHINFO_FILENAME);
                $file_extension = pathinfo($_FILES["product_image"]["name"], PATHINFO_EXTENSION);

                // Sanitize filename
                $original_filename = preg_replace("/[^a-zA-Z0-9]/", "_", $original_filename);
                $file_name = $original_filename . '_' . uniqid() . '.' . $file_extension;
                $target_file = $upload_dir . $file_name;

                // Upload file
                if (move_uploaded_file($_FILES["product_image"]["tmp_name"], $target_file)) {
                    $image_path = "images/products/" . $file_name;

                    // Set proper permissions
                    chmod($target_file, 0644);

                    // Delete old image if exists and not a placeholder
                    if (!empty($current_image) && file_exists("../" . $current_image) && strpos($current_image, "placeholder") === false) {
                        unlink("../" . $current_image);
                    }
                } else {
                    $image_err = "Failed to upload image. Error code: " . $_FILES["product_image"]["error"];
                }
            }
        }
    }

    // Check input errors before updating database
    if (empty($product_name_err) && empty($category_err) && empty($price_err) && empty($alert_level_err) && empty($image_err)) {
        // Prepare an update statement
        $sql = "UPDATE products SET product_name = ?, category = ?, description = ?, price = ?, alert_level = ?, image_path = ? WHERE product_id = ?";

        if ($stmt = $pdo->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bindParam(1, $param_product_name, PDO::PARAM_STR);
            $stmt->bindParam(2, $param_category, PDO::PARAM_STR);
            $stmt->bindParam(3, $param_description, PDO::PARAM_STR);
            $stmt->bindParam(4, $param_price, PDO::PARAM_STR);
            $stmt->bindParam(5, $param_alert_level, PDO::PARAM_INT);
            $stmt->bindParam(6, $param_image_path, PDO::PARAM_STR);
            $stmt->bindParam(7, $param_product_id, PDO::PARAM_INT);

            // Set parameters
            $param_product_name = $product_name;
            $param_category = $category;
            $param_description = $description;
            $param_price = $price;
            $param_alert_level = $alert_level;
            $param_image_path = $image_path;
            $param_product_id = $product_id;

            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                // Redirect to products page
                header("location: products.php?success=Product updated successfully");
                exit();
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            unset($stmt);
        }
    }
} else {
    // Fetch product data
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);

    if ($stmt->rowCount() == 1) {
        $product = $stmt->fetch();
        $product_name = $product['product_name'];
        $category = $product['category'];
        $description = $product['description'];
        $price = $product['price'];
        $quantity = $product['quantity'];
        $alert_level = $product['alert_level'];
        $image_path = $product['image_path'];
    } else {
        // Product not found
        header("location: products.php?error=Product not found");
        exit;
    }
}

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Edit Product</h1>
    <a href="products.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Products
    </a>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-body p-4">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . "?id=" . $product_id); ?>" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="product_name" class="form-label">Product Name</label>
                        <input type="text" name="product_name" id="product_name" class="form-control <?php echo (!empty($product_name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $product_name; ?>" required>
                        <div class="invalid-feedback">
                            <?php echo $product_name_err; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select name="category" id="category" class="form-select <?php echo (!empty($category_err)) ? 'is-invalid' : ''; ?>" required>
                            <option value="" disabled>Select Category</option>
                            <option value="Phones" <?php echo ($category == "Phones") ? "selected" : ""; ?>>Phones</option>
                            <option value="Gaming" <?php echo ($category == "Gaming") ? "selected" : ""; ?>>Gaming</option>
                        </select>
                        <div class="invalid-feedback">
                            <?php echo $category_err; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="price" class="form-label">Price (₦)</label>
                        <input type="number" name="price" id="price" step="1" min="1" class="form-control <?php echo (!empty($price_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $price; ?>" required>
                        <div class="invalid-feedback">
                            <?php echo $price_err; ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">Current Quantity</label>
                                <input type="number" id="quantity" class="form-control" value="<?php echo $quantity; ?>" disabled>
                                <div class="form-text">To update quantity, use Stock In/Out pages</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="alert_level" class="form-label">Alert Level</label>
                                <input type="number" name="alert_level" id="alert_level" min="1" class="form-control <?php echo (!empty($alert_level_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $alert_level; ?>" required>
                                <div class="invalid-feedback">
                                    <?php echo $alert_level_err; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="4"><?php echo $description; ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="product_image" class="form-label">Product Image</label>
                        <input type="file" name="product_image" id="product_image" class="form-control <?php echo (!empty($image_err)) ? 'is-invalid' : ''; ?>" accept="image/*">
                        <div class="invalid-feedback">
                            <?php echo $image_err; ?>
                        </div>
                        <div class="form-text">Leave empty to keep current image. Recommended size: 500x500 pixels.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Current Image</label>
                        <div class="text-center p-3 border rounded" style="background-color: white;">
                            <img src="<?php echo htmlspecialchars(getImageUrl($image_path)); ?>"
                                 alt="<?php echo htmlspecialchars($product_name); ?>"
                                 class="img-fluid" style="max-height: 200px; object-fit: contain;">
                        </div>
                        <input type="hidden" name="current_image" value="<?php echo $image_path; ?>">
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                <a href="products.php" class="btn btn-light me-md-2">Cancel</a>
                <button type="submit" class="btn btn-primary">Update Product</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview
    const productImage = document.getElementById('product_image');

    productImage.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                document.querySelector('.text-center.p-3 img').src = e.target.result;
            }

            reader.readAsDataURL(this.files[0]);
        }
    });

    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }

        form.classList.add('was-validated');
    }, false);
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
