<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Filter parameters
$category = isset($_GET['category']) ? $_GET['category'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';

// Build query based on filters
$query = "SELECT * FROM products";
$where_clauses = [];
$params = [];

if (!empty($category)) {
    $where_clauses[] = "category = ?";
    $params[] = $category;
}

if (!empty($status)) {
    if ($status == 'in-stock') {
        $where_clauses[] = "quantity > alert_level";
    } elseif ($status == 'low-stock') {
        $where_clauses[] = "quantity > 0 AND quantity <= alert_level";
    } elseif ($status == 'out-of-stock') {
        $where_clauses[] = "quantity = 0";
    }
}

if (!empty($where_clauses)) {
    $query .= " WHERE " . implode(" AND ", $where_clauses);
}

$query .= " ORDER BY category, product_name";

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll();

// Calculate totals
$total_products = count($products);
$total_items = 0;
$total_value = 0;
$low_stock_count = 0;
$out_of_stock_count = 0;

foreach ($products as $product) {
    $total_items += $product['quantity'];
    $total_value += $product['quantity'] * $product['price'];
    
    if ($product['quantity'] == 0) {
        $out_of_stock_count++;
    } elseif ($product['quantity'] <= $product['alert_level']) {
        $low_stock_count++;
    }
}

// Get categories for filter
$stmt = $pdo->query("SELECT DISTINCT category FROM products ORDER BY category");
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Inventory Report</h1>
    <button id="printReport" class="btn btn-secondary">
        <i class="fas fa-print me-1"></i> Print Report
    </button>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4 no-print">
    <div class="card-body">
        <form action="" method="get" class="row g-3 align-items-end">
            <div class="col-md-5">
                <label for="category" class="form-label">Category</label>
                <select name="category" id="category" class="form-select">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $cat): ?>
                        <option value="<?php echo $cat; ?>" <?php echo ($category == $cat) ? 'selected' : ''; ?>>
                            <?php echo $cat; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-5">
                <label for="status" class="form-label">Stock Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="in-stock" <?php echo ($status == 'in-stock') ? 'selected' : ''; ?>>In Stock</option>
                    <option value="low-stock" <?php echo ($status == 'low-stock') ? 'selected' : ''; ?>>Low Stock</option>
                    <option value="out-of-stock" <?php echo ($status == 'out-of-stock') ? 'selected' : ''; ?>>Out of Stock</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i> Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Report Header -->
<div class="card border-0 shadow-sm mb-4 print-section">
    <div class="card-body">
        <div class="text-center mb-4">
            <h4 class="mb-0">Griffin Gadgets</h4>
            <p class="mb-0">Inventory Report</p>
            <p class="text-muted">Generated on <?php echo date('F d, Y'); ?></p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Products</h5>
                        <p class="display-6 mb-0"><?php echo $total_products; ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Items</h5>
                        <p class="display-6 mb-0"><?php echo $total_items; ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">Inventory Value</h5>
                        <p class="display-6 mb-0"><?php echo formatCurrency($total_value); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">Low/Out of Stock</h5>
                        <p class="display-6 mb-0"><?php echo $low_stock_count + $out_of_stock_count; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Data Table -->
<div class="card border-0 shadow-sm print-section">
    <div class="card-header bg-white">
        <h5 class="mb-0">Inventory Details</h5>
    </div>
    <div class="card-body">
        <?php if (count($products) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Product</th>
                            <th>Category</th>
                            <th>Unit Price</th>
                            <th>Quantity</th>
                            <th>Value</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <tr>
                                <td><?php echo $product['product_id']; ?></td>
                                <td><?php echo htmlspecialchars($product['product_name']); ?></td>
                                <td><?php echo htmlspecialchars($product['category']); ?></td>
                                <td><?php echo formatCurrency($product['price']); ?></td>
                                <td><?php echo $product['quantity']; ?></td>
                                <td><?php echo formatCurrency($product['quantity'] * $product['price']); ?></td>
                                <td><?php echo getStockStatusBadge($product['quantity'], $product['alert_level']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-active">
                            <th colspan="4">Total</th>
                            <th><?php echo $total_items; ?></th>
                            <th><?php echo formatCurrency($total_value); ?></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <p class="mb-0">No products found matching the selected filters.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Stock Status Summary -->
<div class="card border-0 shadow-sm mt-4 print-section">
    <div class="card-header bg-white">
        <h5 class="mb-0">Stock Status Summary</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card border-success mb-3">
                    <div class="card-body text-center">
                        <h5 class="card-title text-success">In Stock</h5>
                        <p class="display-6 mb-0"><?php echo $total_products - $low_stock_count - $out_of_stock_count; ?></p>
                        <p class="text-muted">products</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-warning mb-3">
                    <div class="card-body text-center">
                        <h5 class="card-title text-warning">Low Stock</h5>
                        <p class="display-6 mb-0"><?php echo $low_stock_count; ?></p>
                        <p class="text-muted">products</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-danger mb-3">
                    <div class="card-body text-center">
                        <h5 class="card-title text-danger">Out of Stock</h5>
                        <p class="display-6 mb-0"><?php echo $out_of_stock_count; ?></p>
                        <p class="text-muted">products</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .navbar, .footer, .btn {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }
    
    .print-section {
        page-break-inside: avoid;
    }
    
    .badge {
        border: 1px solid #000;
    }
    
    .badge.bg-success {
        background-color: #fff !important;
        color: #000 !important;
        border: 1px solid #000;
    }
    
    .badge.bg-warning {
        background-color: #fff !important;
        color: #000 !important;
        border: 1px solid #000;
    }
    
    .badge.bg-danger {
        background-color: #fff !important;
        color: #000 !important;
        border: 1px solid #000;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Print report
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
