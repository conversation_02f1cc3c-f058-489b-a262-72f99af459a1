<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Process delete request
if (isset($_POST['delete_product']) && isset($_POST['product_id'])) {
    $product_id = $_POST['product_id'];

    try {
        // Begin transaction
        $pdo->beginTransaction();

        // Delete related transactions
        $stmt = $pdo->prepare("DELETE FROM stock_transactions WHERE product_id = ?");
        $stmt->execute([$product_id]);

        // Delete product
        $stmt = $pdo->prepare("DELETE FROM products WHERE product_id = ?");
        $stmt->execute([$product_id]);

        // Commit transaction
        $pdo->commit();

        // Redirect with success message
        header("Location: products.php?success=Product deleted successfully");
        exit;
    } catch (PDOException $e) {
        // Rollback transaction on error
        $pdo->rollBack();
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get all products
$stmt = $pdo->query("SELECT * FROM products ORDER BY product_name");
$products = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Products Management</h1>
    <a href="add-product.php" class="btn btn-primary">
        <i class="fas fa-plus-circle me-1"></i> Add New Product
    </a>
</div>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Search and Filter -->
<div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text bg-white border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" id="productSearch" class="form-control border-start-0" placeholder="Search products...">
                </div>
            </div>
            <div class="col-md-3">
                <select id="categoryFilter" class="form-select">
                    <option value="">All Categories</option>
                    <option value="Phones">Phones</option>
                    <option value="Gaming">Gaming</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="stockFilter" class="form-select">
                    <option value="">All Stock Status</option>
                    <option value="in-stock">In Stock</option>
                    <option value="low-stock">Low Stock</option>
                    <option value="out-of-stock">Out of Stock</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="card border-0 shadow-sm">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Image</th>
                        <th>Product Name</th>
                        <th>Category</th>
                        <th>Price</th>
                        <th>Quantity</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($products) > 0): ?>
                        <?php foreach ($products as $product): ?>
                            <tr class="product-item">
                                <td><?php echo $product['product_id']; ?></td>
                                <td>
                                    <img src="<?php echo htmlspecialchars(getImageUrl($product['image_path'])); ?>"
                                         alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                                         class="product-image">
                                </td>
                                <td class="product-name"><?php echo htmlspecialchars($product['product_name']); ?></td>
                                <td class="product-category"><?php echo htmlspecialchars($product['category']); ?></td>
                                <td><?php echo formatCurrency($product['price']); ?></td>
                                <td><?php echo $product['quantity']; ?></td>
                                <td><?php echo getStockStatusBadge($product['quantity'], $product['alert_level']); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="edit-product.php?id=<?php echo $product['product_id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                                data-id="<?php echo $product['product_id']; ?>"
                                                data-name="<?php echo htmlspecialchars($product['product_name']); ?>">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <p class="mb-0">No products found. <a href="add-product.php">Add your first product</a></p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the product: <strong id="deleteItemName"></strong>?</p>
                <p class="text-danger">This action cannot be undone and will also delete all related transaction records.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="product_id" id="deleteProductId">
                    <button type="submit" name="delete_product" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Product search functionality
    const searchInput = document.getElementById('productSearch');
    const categoryFilter = document.getElementById('categoryFilter');
    const stockFilter = document.getElementById('stockFilter');
    const productItems = document.querySelectorAll('.product-item');

    function filterProducts() {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryValue = categoryFilter.value.toLowerCase();
        const stockValue = stockFilter.value;

        productItems.forEach(item => {
            const productName = item.querySelector('.product-name').textContent.toLowerCase();
            const productCategory = item.querySelector('.product-category').textContent.toLowerCase();
            const stockStatus = item.querySelector('td:nth-child(7) .badge').textContent.toLowerCase();

            let showBySearch = productName.includes(searchTerm) || productCategory.includes(searchTerm);
            let showByCategory = categoryValue === '' || productCategory === categoryValue;
            let showByStock = true;

            if (stockValue === 'in-stock') {
                showByStock = stockStatus === 'in stock';
            } else if (stockValue === 'low-stock') {
                showByStock = stockStatus === 'low stock';
            } else if (stockValue === 'out-of-stock') {
                showByStock = stockStatus === 'out of stock';
            }

            if (showBySearch && showByCategory && showByStock) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }

    if (searchInput) searchInput.addEventListener('keyup', filterProducts);
    if (categoryFilter) categoryFilter.addEventListener('change', filterProducts);
    if (stockFilter) stockFilter.addEventListener('change', filterProducts);

    // Delete confirmation
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteItemName = document.getElementById('deleteItemName');
    const deleteProductId = document.getElementById('deleteProductId');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            const productName = this.getAttribute('data-name');

            deleteItemName.textContent = productName;
            deleteProductId.value = productId;

            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
    });
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
