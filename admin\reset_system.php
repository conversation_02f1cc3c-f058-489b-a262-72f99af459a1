<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize response variables
$error = '';
$success = '';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify that the confirm checkbox was checked
    if (!isset($_POST['confirm_reset']) || $_POST['confirm_reset'] !== 'on') {
        $error = "You must confirm that you understand the consequences of this action.";
    }
    // Verify that password was provided
    elseif (!isset($_POST['admin_password']) || empty($_POST['admin_password'])) {
        $error = "Admin password is required to perform this action.";
    }
    else {
        // Get the admin's password from the database
        $admin_id = $_SESSION['user_id'];
        $stmt = $pdo->prepare("SELECT password FROM users WHERE user_id = ? AND role = 'admin'");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch();

        // Verify the password
        if (!$admin || !password_verify($_POST['admin_password'], $admin['password'])) {
            $error = "Invalid password. System reset aborted.";
        } else {
            // Password is correct, proceed with system reset
            try {
                // First, check if tables exist before attempting to delete from them
                // We'll do this outside of a transaction to avoid issues

                // Check for stock_transactions table
                $hasStockTransactions = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'stock_transactions'");
                $hasStockTransactions = ($stmt && $stmt->rowCount() > 0);

                // Check for products table
                $hasProducts = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'products'");
                $hasProducts = ($stmt && $stmt->rowCount() > 0);

                // Check for sales table
                $hasSales = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'sales'");
                $hasSales = ($stmt && $stmt->rowCount() > 0);

                // Check for categories table
                $hasCategories = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
                $hasCategories = ($stmt && $stmt->rowCount() > 0);

                // Now perform the actual reset operations
                // We'll use separate try-catch blocks for each operation to ensure
                // that if one fails, the others can still proceed

                // Temporarily disable foreign key checks
                try {
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
                } catch (Exception $e) {
                    $error .= "Error disabling foreign key checks: " . $e->getMessage() . ". ";
                }

                // 1. Delete stock transactions
                if ($hasStockTransactions) {
                    try {
                        $pdo->exec("TRUNCATE TABLE stock_transactions");
                    } catch (Exception $e) {
                        $error .= "Error clearing stock transactions: " . $e->getMessage() . ". ";
                    }
                }

                // 2. Delete products
                if ($hasProducts) {
                    try {
                        $pdo->exec("TRUNCATE TABLE products");
                    } catch (Exception $e) {
                        $error .= "Error clearing products: " . $e->getMessage() . ". ";
                    }
                }

                // 3. Delete sales records
                if ($hasSales) {
                    try {
                        $pdo->exec("TRUNCATE TABLE sales");
                    } catch (Exception $e) {
                        $error .= "Error clearing sales: " . $e->getMessage() . ". ";
                    }
                }

                // Re-enable foreign key checks
                try {
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
                } catch (Exception $e) {
                    $error .= "Error re-enabling foreign key checks: " . $e->getMessage() . ". ";
                }

                // 4. Ensure default categories exist
                if ($hasCategories) {
                    try {
                        // Don't delete categories, just make sure default ones exist
                        $categories = ['Phones', 'Laptops', 'Accessories', 'Tablets', 'Wearables', 'Gaming'];
                        foreach ($categories as $category) {
                            $check = $pdo->prepare("SELECT * FROM categories WHERE category_name = ?");
                            $check->execute([$category]);
                            if ($check->rowCount() == 0) {
                                // Category doesn't exist, add it
                                $insert = $pdo->prepare("INSERT INTO categories (category_name) VALUES (?)");
                                $insert->execute([$category]);
                            }
                        }
                    } catch (Exception $e) {
                        $error .= "Error managing categories: " . $e->getMessage() . ". ";
                    }
                }

                // If we have no errors, set success message
                if (empty($error)) {
                    $success = "System has been successfully reset. All products and sales data have been cleared.";

                    // Log the reset action
                    try {
                        logAction($pdo, 'system_reset', 'Admin reset the system - cleared all products and sales data', $_SESSION['user_id']);
                    } catch (Exception $e) {
                        // Just log to error_log if logging fails, don't show to user
                        error_log("Failed to log reset action: " . $e->getMessage());
                    }
                }

            } catch (Exception $e) {
                // Catch any other errors
                $error = "An error occurred during system reset: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Reset - Griffin Gadgets</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/all.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        .reset-result {
            max-width: 600px;
            margin: 100px auto;
            text-align: center;
        }
        .icon-container {
            margin-bottom: 20px;
        }
        .icon-container i {
            font-size: 5rem;
        }
        .success-icon {
            color: #28a745;
        }
        .error-icon {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="reset-result">
            <?php if ($success): ?>
                <div class="icon-container">
                    <i class="fas fa-check-circle success-icon"></i>
                </div>
                <h2>System Reset Successful</h2>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
                <p>The system has been reset to its initial state. All products and sales data have been cleared.</p>
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-primary">Return to Dashboard</a>
                </div>
            <?php elseif ($error): ?>
                <div class="icon-container">
                    <i class="fas fa-times-circle error-icon"></i>
                </div>
                <h2>System Reset Failed</h2>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <p>The system reset operation could not be completed. Please try again.</p>
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-primary">Return to Dashboard</a>
                </div>
            <?php else: ?>
                <div class="icon-container">
                    <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                </div>
                <h2>Unauthorized Access</h2>
                <div class="alert alert-warning">
                    Direct access to this page is not allowed.
                </div>
                <p>Please use the reset button on the dashboard to perform a system reset.</p>
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-primary">Return to Dashboard</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="../js/bootstrap.bundle.min.js"></script>
</body>
</html>
