<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Set default date range to today
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Get sales data
$stmt = $pdo->prepare("
    SELECT 
        p.product_id,
        p.product_name,
        p.category,
        p.price,
        SUM(t.quantity) as total_quantity,
        SUM(t.quantity * p.price) as total_value
    FROM 
        stock_transactions t
    JOIN 
        products p ON t.product_id = p.product_id
    WHERE 
        t.transaction_type = 'out' 
        AND DATE(t.transaction_date) BETWEEN ? AND ?
    GROUP BY 
        p.product_id, p.product_name, p.category, p.price
    ORDER BY 
        total_value DESC
");
$stmt->execute([$start_date, $end_date]);
$sales_data = $stmt->fetchAll();

// Calculate totals
$total_items_sold = 0;
$total_sales_value = 0;

foreach ($sales_data as $item) {
    $total_items_sold += $item['total_quantity'];
    $total_sales_value += $item['total_value'];
}

// Get daily sales for chart
$stmt = $pdo->prepare("
    SELECT 
        DATE(t.transaction_date) as sale_date,
        SUM(t.quantity * p.price) as daily_sales
    FROM 
        stock_transactions t
    JOIN 
        products p ON t.product_id = p.product_id
    WHERE 
        t.transaction_type = 'out' 
        AND DATE(t.transaction_date) BETWEEN ? AND ?
    GROUP BY 
        DATE(t.transaction_date)
    ORDER BY 
        sale_date
");
$stmt->execute([$start_date, $end_date]);
$daily_sales = $stmt->fetchAll();

// Format data for chart
$chart_labels = [];
$chart_data = [];

foreach ($daily_sales as $day) {
    $chart_labels[] = date('M d', strtotime($day['sale_date']));
    $chart_data[] = $day['daily_sales'];
}

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Sales Report</h1>
    <button id="printReport" class="btn btn-secondary">
        <i class="fas fa-print me-1"></i> Print Report
    </button>
</div>

<!-- Date Range Filter -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form action="" method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" id="start_date" name="start_date" class="form-control" value="<?php echo $start_date; ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" id="end_date" name="end_date" class="form-control" value="<?php echo $end_date; ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i> Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Report Header -->
<div class="card border-0 shadow-sm mb-4 print-section">
    <div class="card-body">
        <div class="text-center mb-4">
            <h4 class="mb-0">Griffin Gadgets</h4>
            <p class="mb-0">Sales Report</p>
            <p class="text-muted">
                <?php 
                if ($start_date == $end_date) {
                    echo date('F d, Y', strtotime($start_date));
                } else {
                    echo date('F d, Y', strtotime($start_date)) . ' - ' . date('F d, Y', strtotime($end_date));
                }
                ?>
            </p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Sales</h5>
                        <p class="display-6 mb-0"><?php echo formatCurrency($total_sales_value); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Items Sold</h5>
                        <p class="display-6 mb-0"><?php echo $total_items_sold; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Chart -->
<div class="card border-0 shadow-sm mb-4 no-print">
    <div class="card-header bg-white">
        <h5 class="mb-0">Sales Trend</h5>
    </div>
    <div class="card-body">
        <canvas id="salesChart" height="100"></canvas>
    </div>
</div>

<!-- Sales Data Table -->
<div class="card border-0 shadow-sm print-section">
    <div class="card-header bg-white">
        <h5 class="mb-0">Sales Details</h5>
    </div>
    <div class="card-body">
        <?php if (count($sales_data) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th>Unit Price</th>
                            <th>Quantity Sold</th>
                            <th>Total Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sales_data as $item): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                <td><?php echo htmlspecialchars($item['category']); ?></td>
                                <td><?php echo formatCurrency($item['price']); ?></td>
                                <td><?php echo $item['total_quantity']; ?></td>
                                <td><?php echo formatCurrency($item['total_value']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-active">
                            <th colspan="3">Total</th>
                            <th><?php echo $total_items_sold; ?></th>
                            <th><?php echo formatCurrency($total_sales_value); ?></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <p class="mb-0">No sales data found for the selected date range.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .navbar, .footer, .btn {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }
    
    .print-section {
        page-break-inside: avoid;
    }
}
</style>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Print report
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });
    
    // Sales chart
    const ctx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($chart_labels); ?>,
            datasets: [{
                label: 'Daily Sales',
                data: <?php echo json_encode($chart_data); ?>,
                backgroundColor: 'rgba(52, 152, 219, 0.2)',
                borderColor: 'rgba(52, 152, 219, 1)',
                borderWidth: 2,
                tension: 0.3,
                pointBackgroundColor: 'rgba(52, 152, 219, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Sales: $' + context.raw.toFixed(2);
                        }
                    }
                }
            }
        }
    });
    
    // Date validation
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    endDateInput.addEventListener('change', function() {
        if (startDateInput.value && this.value && this.value < startDateInput.value) {
            alert('End date cannot be earlier than start date');
            this.value = startDateInput.value;
        }
    });
    
    startDateInput.addEventListener('change', function() {
        if (endDateInput.value && this.value && this.value > endDateInput.value) {
            endDateInput.value = this.value;
        }
    });
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
