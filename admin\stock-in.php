<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize variables
$product_id = $quantity = $notes = "";
$product_id_err = $quantity_err = "";
$success_message = $error_message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate product
    if (empty($_POST["product_id"])) {
        $product_id_err = "Please select a product.";
    } else {
        $product_id = $_POST["product_id"];
    }

    // Validate quantity
    if (empty($_POST["quantity"])) {
        $quantity_err = "Please enter a quantity.";
    } elseif (!is_numeric($_POST["quantity"]) || $_POST["quantity"] <= 0) {
        $quantity_err = "Please enter a valid quantity.";
    } else {
        $quantity = $_POST["quantity"];
    }

    // Get notes
    $notes = trim($_POST["notes"]);

    // Check input errors before inserting into database
    if (empty($product_id_err) && empty($quantity_err)) {
        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Get current product quantity
            $stmt = $pdo->prepare("SELECT quantity FROM products WHERE product_id = ?");
            $stmt->execute([$product_id]);
            $current_quantity = $stmt->fetch()['quantity'];

            // Calculate new quantity
            $new_quantity = $current_quantity + $quantity;

            // Update product quantity
            $stmt = $pdo->prepare("UPDATE products SET quantity = ? WHERE product_id = ?");
            $stmt->execute([$new_quantity, $product_id]);

            // Log stock transaction - verify user_id exists first
            $user_id = $_SESSION['user_id'] ?? null;

            // Verify user exists in database
            if ($user_id) {
                $check_user = $pdo->prepare("SELECT user_id FROM users WHERE user_id = ?");
                $check_user->execute([$user_id]);

                if ($check_user->rowCount() > 0) {
                    // User exists, proceed with transaction
                    $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, 'in', ?, ?)");
                    $stmt->execute([$product_id, $user_id, $quantity, $notes]);
                } else {
                    // User doesn't exist, use a default admin user from the database
                    $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
                    $admin = $get_admin->fetch();

                    if ($admin && isset($admin['user_id'])) {
                        $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, 'in', ?, ?)");
                        $stmt->execute([$product_id, $admin['user_id'], $quantity, $notes . ' (system)']);
                    } else {
                        // No admin found, just update the product quantity without logging
                        error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
                    }
                }
            } else {
                // No user_id in session, use a default admin
                $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
                $admin = $get_admin->fetch();

                if ($admin && isset($admin['user_id'])) {
                    $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, 'in', ?, ?)");
                    $stmt->execute([$product_id, $admin['user_id'], $quantity, $notes . ' (system)']);
                } else {
                    // No admin found, just update the product quantity without logging
                    error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
                }
            }

            // Commit transaction
            $pdo->commit();

            // Set success message
            $success_message = "Stock added successfully!";

            // Clear form data
            $product_id = $quantity = $notes = "";
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

// Get all products for dropdown
$stmt = $pdo->query("SELECT * FROM products ORDER BY product_name");
$products = $stmt->fetchAll();

// Get recent stock-in transactions
$stmt = $pdo->prepare("
    SELECT t.*, p.product_name, u.username
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    JOIN users u ON t.user_id = u.user_id
    WHERE t.transaction_type = 'in'
    ORDER BY t.transaction_date DESC
    LIMIT 10
");
$stmt->execute();
$recent_transactions = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Stock In</h1>
    <a href="products.php" class="btn btn-secondary">
        <i class="fas fa-boxes me-1"></i> View All Products
    </a>
</div>

<?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="row g-4">
    <div class="col-lg-5">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">Add Stock</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="product_id" class="form-label">Select Product</label>
                        <select name="product_id" id="product_id" class="form-select <?php echo (!empty($product_id_err)) ? 'is-invalid' : ''; ?>" required>
                            <option value="" selected disabled>-- Select Product --</option>
                            <?php foreach ($products as $product): ?>
                                <option value="<?php echo $product['product_id']; ?>"
                                        data-price="<?php echo $product['price']; ?>"
                                        data-stock="<?php echo $product['quantity']; ?>"
                                        <?php echo ($product_id == $product['product_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($product['product_name']); ?> (<?php echo htmlspecialchars($product['category']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">
                            <?php echo $product_id_err; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="current_stock" class="form-label">Current Stock</label>
                            <input type="text" id="current_stock" class="form-control" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="price" class="form-label">Unit Price</label>
                            <input type="text" id="price" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity to Add</label>
                        <input type="number" name="quantity" id="quantity" min="1" class="form-control <?php echo (!empty($quantity_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $quantity; ?>" required>
                        <div class="invalid-feedback">
                            <?php echo $quantity_err; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (Optional)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"><?php echo $notes; ?></textarea>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i> Add Stock
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-7">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">Recent Stock In Transactions</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Added By</th>
                                <th>Date</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($recent_transactions) > 0): ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($transaction['product_name']); ?></td>
                                        <td><?php echo $transaction['quantity']; ?></td>
                                        <td><?php echo htmlspecialchars($transaction['username']); ?></td>
                                        <td><?php echo date('M d, Y H:i', strtotime($transaction['transaction_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['notes'] ?: '-'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center">No recent transactions found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic product selection
    const productSelect = document.getElementById('product_id');
    const priceField = document.getElementById('price');
    const currentStockField = document.getElementById('current_stock');

    if (productSelect && priceField && currentStockField) {
        productSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.getAttribute('data-price');
            const stock = selectedOption.getAttribute('data-stock');

            priceField.value = price ? '$' + parseFloat(price).toFixed(2) : '';
            currentStockField.value = stock || '0';
        });

        // Trigger change event if a product is already selected
        if (productSelect.value) {
            productSelect.dispatchEvent(new Event('change'));
        }
    }

    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }

        form.classList.add('was-validated');
    }, false);
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
