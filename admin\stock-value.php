<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Get products with their stock value
$stmt = $pdo->query("
    SELECT
        product_id,
        product_name,
        quantity,
        price,
        (quantity * price) as stock_value,
        alert_level,
        created_at
    FROM
        products
    ORDER BY
        stock_value DESC
");
$products = $stmt->fetchAll();

// Calculate total stock value
$total_stock_value = 0;
foreach ($products as $product) {
    $total_stock_value += $product['stock_value'];
}

// Include header
include '../includes/header.php';
?>

<style>
    .stock-value-card {
        transition: all 0.3s ease;
    }

    .stock-value-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .search-container {
        position: relative;
    }

    .search-container i {
        position: absolute;
        left: 10px;
        top: 10px;
        color: #6c757d;
    }

    #productSearch {
        padding-left: 30px;
        border-radius: 20px;
    }

    .value-high {
        color: #2ecc71;
    }

    .value-medium {
        color: #3498db;
    }

    .value-low {
        color: #e67e22;
    }

    .value-zero {
        color: #e74c3c;
    }
</style>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Stock Value Report</h1>
            <p class="text-muted">Overview of inventory value</p>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="dashboard.php" class="btn btn-outline-primary rounded-pill">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-6 col-lg-3">
            <div class="card stock-value-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-dollar-sign fa-2x text-success"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-muted mb-1">Total Stock Value</h6>
                            <h2 class="mb-0 fw-bold text-success"><?php echo formatCurrency($total_stock_value); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3">
            <div class="card stock-value-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-boxes fa-2x text-primary"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-muted mb-1">Total Products</h6>
                            <h2 class="mb-0 fw-bold text-primary"><?php echo count($products); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3">
            <div class="card stock-value-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-chart-line fa-2x text-info"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-muted mb-1">Average Product Value</h6>
                            <h2 class="mb-0 fw-bold text-info">
                                <?php
                                $avg_value = count($products) > 0 ? $total_stock_value / count($products) : 0;
                                echo formatCurrency($avg_value);
                                ?>
                            </h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3">
            <div class="card stock-value-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-calendar-alt fa-2x text-warning"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-muted mb-1">Report Date</h6>
                            <h2 class="mb-0 fw-bold text-warning"><?php echo date('M d, Y'); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Value Table -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header py-3" style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white;">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Stock Value by Product</h5>
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="productSearch" class="form-control form-control-sm" placeholder="Search products...">
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle" id="productsTable">
                    <thead>
                        <tr>
                            <th class="text-center">Image</th>
                            <th>Product Name</th>
                            <th class="text-center">Quantity</th>
                            <th class="text-end">Unit Price</th>
                            <th class="text-end">Stock Value</th>
                            <th class="text-center">Last Updated</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <?php
                            // Determine value class based on stock value
                            $valueClass = 'value-zero';
                            if ($product['stock_value'] > 0) {
                                if ($product['stock_value'] > 1000000) {
                                    $valueClass = 'value-high';
                                } elseif ($product['stock_value'] > 500000) {
                                    $valueClass = 'value-medium';
                                } else {
                                    $valueClass = 'value-low';
                                }
                            }
                            ?>
                            <tr>
                                <td class="text-center" style="width: 80px;">
                                    <?php
                                    // Determine image based on product name
                                    $imageName = '';
                                    $productNameLower = strtolower($product['product_name']);

                                    if (strpos($productNameLower, 'iphone 13') !== false || strpos($productNameLower, '13 pro') !== false) {
                                        $imageName = '13pro.jpg';
                                    } elseif (strpos($productNameLower, 'iphone 14') !== false || strpos($productNameLower, '14') !== false) {
                                        $imageName = '14pro.jpg';
                                    } elseif (strpos($productNameLower, 's22') !== false || strpos($productNameLower, 'galaxy s22') !== false) {
                                        $imageName = 's22.jpg';
                                    } elseif (strpos($productNameLower, 'fold') !== false || strpos($productNameLower, 'z fold') !== false) {
                                        $imageName = 'zfold4.jpg';
                                    } elseif (strpos($productNameLower, 'playstation') !== false || strpos($productNameLower, 'ps5') !== false) {
                                        $imageName = 'playstation5.jpg';
                                    }

                                    if (!empty($imageName)):
                                    ?>
                                        <img src="../images/products/<?php echo $imageName; ?>"
                                            alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                                            class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center"
                                            style="width: 60px; height: 60px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($product['product_name']); ?></strong>
                                </td>
                                <td class="text-center">
                                    <?php if ($product['quantity'] <= 0): ?>
                                        <span class="badge bg-danger">Out of Stock</span>
                                    <?php elseif ($product['quantity'] <= $product['alert_level']): ?>
                                        <span class="badge bg-warning text-dark">Low: <?php echo $product['quantity']; ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-success"><?php echo $product['quantity']; ?></span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end"><?php echo formatCurrency($product['price']); ?></td>
                                <td class="text-end fw-bold <?php echo $valueClass; ?>"><?php echo formatCurrency($product['stock_value']); ?></td>
                                <td class="text-center"><?php echo date('M d, Y', strtotime($product['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Value Distribution Chart -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header py-3" style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white;">
            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Stock Value Distribution</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <canvas id="stockValueChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('productSearch');
    const table = document.getElementById('productsTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();

        for (let i = 0; i < rows.length; i++) {
            const productName = rows[i].getElementsByTagName('td')[1].textContent.toLowerCase();

            if (productName.includes(searchTerm)) {
                rows[i].style.display = '';
            } else {
                rows[i].style.display = 'none';
            }
        }
    });

    // Chart.js implementation
    const ctx = document.getElementById('stockValueChart').getContext('2d');

    // Prepare data for chart
    const productNames = [];
    const stockValues = [];
    const backgroundColors = [];

    <?php
    // Get top 5 products by stock value for the chart
    $topProducts = array_slice($products, 0, 5);
    $otherValue = 0;

    // Calculate value for "Others" category
    if (count($products) > 5) {
        for ($i = 5; $i < count($products); $i++) {
            $otherValue += $products[$i]['stock_value'];
        }
    }

    // Generate JavaScript arrays for chart data
    foreach ($topProducts as $index => $product) {
        echo "productNames.push('" . addslashes($product['product_name']) . "');\n";
        echo "stockValues.push(" . $product['stock_value'] . ");\n";

        // Different colors for each product
        $colors = ['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6'];
        echo "backgroundColors.push('" . $colors[$index % count($colors)] . "');\n";
    }

    // Add "Others" category if there are more than 5 products
    if ($otherValue > 0) {
        echo "productNames.push('Others');\n";
        echo "stockValues.push(" . $otherValue . ");\n";
        echo "backgroundColors.push('#95a5a6');\n";
    }
    ?>

    const stockValueChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: productNames,
            datasets: [{
                data: stockValues,
                backgroundColor: backgroundColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        font: {
                            size: 12
                        },
                        padding: 20
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const formattedValue = new Intl.NumberFormat('en-NG', {
                                style: 'currency',
                                currency: 'NGN',
                                minimumFractionDigits: 2
                            }).format(value);

                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);

                            return `${context.label}: ${formattedValue} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
