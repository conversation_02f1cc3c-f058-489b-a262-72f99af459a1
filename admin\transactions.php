<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Set default date range to last 7 days
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$transaction_type = isset($_GET['transaction_type']) ? $_GET['transaction_type'] : '';
$product_id = isset($_GET['product_id']) ? $_GET['product_id'] : '';

// Build query based on filters
$query = "
    SELECT t.*, p.product_name, p.price, u.username 
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    JOIN users u ON t.user_id = u.user_id
    WHERE DATE(t.transaction_date) BETWEEN ? AND ?
";
$params = [$start_date, $end_date];

if (!empty($transaction_type)) {
    $query .= " AND t.transaction_type = ?";
    $params[] = $transaction_type;
}

if (!empty($product_id)) {
    $query .= " AND t.product_id = ?";
    $params[] = $product_id;
}

$query .= " ORDER BY t.transaction_date DESC";

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$transactions = $stmt->fetchAll();

// Get products for filter
$stmt = $pdo->query("SELECT product_id, product_name FROM products ORDER BY product_name");
$products = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Transaction History</h1>
    <button id="printReport" class="btn btn-secondary">
        <i class="fas fa-print me-1"></i> Print Report
    </button>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4 no-print">
    <div class="card-body">
        <form action="" method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" id="start_date" name="start_date" class="form-control" value="<?php echo $start_date; ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" id="end_date" name="end_date" class="form-control" value="<?php echo $end_date; ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-2">
                <label for="transaction_type" class="form-label">Type</label>
                <select name="transaction_type" id="transaction_type" class="form-select">
                    <option value="">All Types</option>
                    <option value="in" <?php echo ($transaction_type == 'in') ? 'selected' : ''; ?>>Stock In</option>
                    <option value="out" <?php echo ($transaction_type == 'out') ? 'selected' : ''; ?>>Stock Out</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="product_id" class="form-label">Product</label>
                <select name="product_id" id="product_id" class="form-select">
                    <option value="">All Products</option>
                    <?php foreach ($products as $product): ?>
                        <option value="<?php echo $product['product_id']; ?>" <?php echo ($product_id == $product['product_id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($product['product_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Report Header -->
<div class="card border-0 shadow-sm mb-4 print-section">
    <div class="card-body">
        <div class="text-center mb-4">
            <h4 class="mb-0">Griffin Gadgets</h4>
            <p class="mb-0">Transaction History Report</p>
            <p class="text-muted">
                <?php 
                if ($start_date == $end_date) {
                    echo date('F d, Y', strtotime($start_date));
                } else {
                    echo date('F d, Y', strtotime($start_date)) . ' - ' . date('F d, Y', strtotime($end_date));
                }
                ?>
            </p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Transactions</h5>
                        <p class="display-6 mb-0"><?php echo count($transactions); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Stock In</h5>
                        <p class="display-6 mb-0">
                            <?php 
                            echo count(array_filter($transactions, function($t) {
                                return $t['transaction_type'] == 'in';
                            }));
                            ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Stock Out</h5>
                        <p class="display-6 mb-0">
                            <?php 
                            echo count(array_filter($transactions, function($t) {
                                return $t['transaction_type'] == 'out';
                            }));
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="card border-0 shadow-sm print-section">
    <div class="card-header bg-white">
        <h5 class="mb-0">Transaction Details</h5>
    </div>
    <div class="card-body">
        <?php if (count($transactions) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Date & Time</th>
                            <th>Product</th>
                            <th>Type</th>
                            <th>Quantity</th>
                            <th>Value</th>
                            <th>User</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($transactions as $transaction): ?>
                            <tr>
                                <td><?php echo $transaction['transaction_id']; ?></td>
                                <td><?php echo date('M d, Y H:i', strtotime($transaction['transaction_date'])); ?></td>
                                <td><?php echo htmlspecialchars($transaction['product_name']); ?></td>
                                <td>
                                    <?php if ($transaction['transaction_type'] == 'in'): ?>
                                        <span class="badge bg-success">Stock In</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Stock Out</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $transaction['quantity']; ?></td>
                                <td><?php echo formatCurrency($transaction['quantity'] * $transaction['price']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['username']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['notes'] ?: '-'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <p class="mb-0">No transactions found for the selected filters.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .navbar, .footer, .btn {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }
    
    .print-section {
        page-break-inside: avoid;
    }
    
    .badge.bg-success {
        background-color: #fff !important;
        color: #000 !important;
        border: 1px solid #000;
    }
    
    .badge.bg-danger {
        background-color: #fff !important;
        color: #000 !important;
        border: 1px solid #000;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Print report
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });
    
    // Date validation
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    endDateInput.addEventListener('change', function() {
        if (startDateInput.value && this.value && this.value < startDateInput.value) {
            alert('End date cannot be earlier than start date');
            this.value = startDateInput.value;
        }
    });
    
    startDateInput.addEventListener('change', function() {
        if (endDateInput.value && this.value && this.value > endDateInput.value) {
            endDateInput.value = this.value;
        }
    });
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
