<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Process user actions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Approve user
    if (isset($_POST['approve_user']) && isset($_POST['user_id'])) {
        $user_id = $_POST['user_id'];

        $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE user_id = ? AND role = 'storekeeper'");
        if ($stmt->execute([$user_id])) {
            header("Location: users.php?success=User approved successfully");
            exit;
        } else {
            $error_message = "Error approving user";
        }
    }

    // Reject/Delete user
    if (isset($_POST['delete_user']) && isset($_POST['user_id'])) {
        $user_id = $_POST['user_id'];

        try {
            // Start transaction
            $pdo->beginTransaction();

            // Check if user has any stock transactions
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM stock_transactions WHERE user_id = ?");
            $check_stmt->execute([$user_id]);
            $has_transactions = ($check_stmt->fetchColumn() > 0);

            if ($has_transactions) {
                // Option 1: Reassign transactions to admin user
                $admin_stmt = $pdo->prepare("SELECT user_id FROM users WHERE role = 'admin' AND user_id != ? LIMIT 1");
                $admin_stmt->execute([$user_id]);
                $admin_id = $admin_stmt->fetchColumn();

                if ($admin_id) {
                    // Update all transactions to be owned by admin
                    $update_stmt = $pdo->prepare("UPDATE stock_transactions SET user_id = ? WHERE user_id = ?");
                    $update_stmt->execute([$admin_id, $user_id]);

                    // Now delete the user
                    $delete_stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ? AND role = 'storekeeper'");
                    $delete_stmt->execute([$user_id]);

                    // Commit transaction
                    $pdo->commit();

                    header("Location: users.php?success=User deleted successfully. Their transactions have been reassigned to an admin user.");
                    exit;
                } else {
                    // No other admin found, can't delete
                    $pdo->rollBack();
                    $error_message = "Cannot delete user: This user has stock transactions and there is no other admin to reassign them to.";
                }
            } else {
                // No transactions, just delete the user
                $delete_stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ? AND role = 'storekeeper'");
                $delete_stmt->execute([$user_id]);

                // Commit transaction
                $pdo->commit();

                header("Location: users.php?success=User deleted successfully");
                exit;
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error_message = "Error deleting user: " . $e->getMessage();
        }
    }

    // Reset password
    if (isset($_POST['reset_password']) && isset($_POST['user_id'])) {
        $user_id = $_POST['user_id'];

        // Set default password: 12345678
        $default_password = password_hash('12345678', PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("UPDATE users SET password = ?, password_reset_requested = FALSE WHERE user_id = ? AND role = 'storekeeper'");
        if ($stmt->execute([$default_password, $user_id])) {
            header("Location: users.php?success=Password reset successfully to '12345678'");
            exit;
        } else {
            $error_message = "Error resetting password";
        }
    }

    // Activate/Deactivate user
    if (isset($_POST['toggle_status']) && isset($_POST['user_id'])) {
        $user_id = $_POST['user_id'];
        $new_status = $_POST['new_status'];

        $stmt = $pdo->prepare("UPDATE users SET status = ? WHERE user_id = ? AND role = 'storekeeper'");
        if ($stmt->execute([$new_status, $user_id])) {
            $status_message = $new_status === 'active' ? 'activated' : 'deactivated';
            header("Location: users.php?success=User $status_message successfully");
            exit;
        } else {
            $error_message = "Error updating user status";
        }
    }
}

// Get all users except current admin
$stmt = $pdo->prepare("
    SELECT * FROM users
    WHERE user_id != ?
    ORDER BY
        CASE
            WHEN status = 'pending' THEN 1
            WHEN password_reset_requested = TRUE THEN 2
            WHEN status = 'inactive' THEN 3
            ELSE 4
        END,
        role DESC,
        username ASC
");
$stmt->execute([$_SESSION['user_id']]);
$users = $stmt->fetchAll();

// Count pending users
$pending_count = 0;
$reset_requests_count = 0;

foreach ($users as $user) {
    if ($user['status'] === 'pending') {
        $pending_count++;
    }
    if ($user['password_reset_requested']) {
        $reset_requests_count++;
    }
}

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">User Management</h1>
        <p class="text-muted">Manage storekeepers and user accounts</p>
    </div>
    <div>
        <?php if ($pending_count > 0): ?>
            <span class="badge bg-warning text-dark me-2">
                <?php echo $pending_count; ?> Pending Approval<?php echo $pending_count > 1 ? 's' : ''; ?>
            </span>
        <?php endif; ?>

        <?php if ($reset_requests_count > 0): ?>
            <span class="badge bg-info text-dark">
                <?php echo $reset_requests_count; ?> Password Reset Request<?php echo $reset_requests_count > 1 ? 's' : ''; ?>
            </span>
        <?php endif; ?>
    </div>
</div>

<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo htmlspecialchars($_GET['success']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Users Table -->
<div class="card border-0 shadow-sm">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($users) > 0): ?>
                        <?php foreach ($users as $user): ?>
                            <tr class="<?php echo $user['status'] === 'pending' ? 'table-warning' : ($user['password_reset_requested'] ? 'table-info' : ''); ?>">
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <span class="badge <?php echo $user['role'] === 'admin' ? 'bg-danger' : 'bg-primary'; ?>">
                                        <?php echo ucfirst($user['role']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($user['status'] === 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php elseif ($user['status'] === 'pending'): ?>
                                        <span class="badge bg-warning text-dark">Pending Approval</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>

                                    <?php if ($user['password_reset_requested']): ?>
                                        <span class="badge bg-info text-dark ms-1">Password Reset</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'Never'; ?>
                                </td>
                                <td>
                                    <?php if ($user['role'] === 'storekeeper'): ?>
                                        <div class="btn-group">
                                            <?php if ($user['status'] === 'pending'): ?>
                                                <button type="button" class="btn btn-sm btn-success approve-btn"
                                                        data-id="<?php echo $user['user_id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($user['full_name']); ?>">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger delete-btn"
                                                        data-id="<?php echo $user['user_id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($user['full_name']); ?>">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php else: ?>
                                                <?php if ($user['password_reset_requested']): ?>
                                                    <button type="button" class="btn btn-sm btn-info reset-btn"
                                                            data-id="<?php echo $user['user_id']; ?>"
                                                            data-name="<?php echo htmlspecialchars($user['full_name']); ?>">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <?php if ($user['status'] === 'active'): ?>
                                                    <button type="button" class="btn btn-sm btn-warning status-btn"
                                                            data-id="<?php echo $user['user_id']; ?>"
                                                            data-name="<?php echo htmlspecialchars($user['full_name']); ?>"
                                                            data-status="inactive">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-success status-btn"
                                                            data-id="<?php echo $user['user_id']; ?>"
                                                            data-name="<?php echo htmlspecialchars($user['full_name']); ?>"
                                                            data-status="active">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <button type="button" class="btn btn-sm btn-danger delete-btn"
                                                        data-id="<?php echo $user['user_id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($user['full_name']); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center">No users found</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Approve User Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">Approve User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to approve <strong id="approveUserName"></strong>?</p>
                <p>This will grant them access to the system as a storekeeper.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="user_id" id="approveUserId">
                    <button type="submit" name="approve_user" class="btn btn-success">Approve</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
                <p><strong>Note:</strong> If this user has recorded any stock transactions, those transactions will be reassigned to an admin user to maintain data integrity.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="user_id" id="deleteUserId">
                    <button type="submit" name="delete_user" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetModal" tabindex="-1" aria-labelledby="resetModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetModalLabel">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reset the password for <strong id="resetUserName"></strong>?</p>
                <p>The password will be reset to: <code>12345678</code></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="user_id" id="resetUserId">
                    <button type="submit" name="reset_password" class="btn btn-info">Reset Password</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Toggle Status Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">Change User Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="statusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="user_id" id="statusUserId">
                    <input type="hidden" name="new_status" id="newStatus">
                    <button type="submit" name="toggle_status" id="statusButton" class="btn">Confirm</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Approve user
    const approveButtons = document.querySelectorAll('.approve-btn');
    const approveUserName = document.getElementById('approveUserName');
    const approveUserId = document.getElementById('approveUserId');

    approveButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const userName = this.getAttribute('data-name');

            approveUserName.textContent = userName;
            approveUserId.value = userId;

            const approveModal = new bootstrap.Modal(document.getElementById('approveModal'));
            approveModal.show();
        });
    });

    // Delete user
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteUserName = document.getElementById('deleteUserName');
    const deleteUserId = document.getElementById('deleteUserId');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const userName = this.getAttribute('data-name');

            deleteUserName.textContent = userName;
            deleteUserId.value = userId;

            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
    });

    // Reset password
    const resetButtons = document.querySelectorAll('.reset-btn');
    const resetUserName = document.getElementById('resetUserName');
    const resetUserId = document.getElementById('resetUserId');

    resetButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const userName = this.getAttribute('data-name');

            resetUserName.textContent = userName;
            resetUserId.value = userId;

            const resetModal = new bootstrap.Modal(document.getElementById('resetModal'));
            resetModal.show();
        });
    });

    // Toggle status
    const statusButtons = document.querySelectorAll('.status-btn');
    const statusMessage = document.getElementById('statusMessage');
    const statusUserId = document.getElementById('statusUserId');
    const newStatus = document.getElementById('newStatus');
    const statusButton = document.getElementById('statusButton');

    statusButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const userName = this.getAttribute('data-name');
            const status = this.getAttribute('data-status');

            statusUserId.value = userId;
            newStatus.value = status;

            if (status === 'active') {
                statusMessage.innerHTML = `Are you sure you want to <strong>activate</strong> ${userName}?`;
                statusButton.className = 'btn btn-success';
                statusButton.textContent = 'Activate';
            } else {
                statusMessage.innerHTML = `Are you sure you want to <strong>deactivate</strong> ${userName}?`;
                statusButton.className = 'btn btn-warning';
                statusButton.textContent = 'Deactivate';
            }

            const statusModal = new bootstrap.Modal(document.getElementById('statusModal'));
            statusModal.show();
        });
    });
});
</script>

<?php
// Include password toggle script
echo '<script src="../js/password-toggle.js"></script>';

// Include footer
include '../includes/footer.php';
?>
