<?php
// Include database connection
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Get all products with their current stock levels
$stmt = $pdo->query("SELECT product_id, product_name, quantity, alert_level FROM products ORDER BY product_name");
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Return as JSON
header('Content-Type: application/json');
echo json_encode($products);
?>
