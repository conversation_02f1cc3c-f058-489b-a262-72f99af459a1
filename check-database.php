<?php
// Debug script to check database schema

// Include database connection
require_once 'includes/db_connect.php';

echo "<h1>Database Schema Check</h1>";

// Check if the products table has the image_path column
echo "<h2>Products Table Schema</h2>";
try {
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasImagePath = false;
    $hasCategory = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        foreach ($column as $key => $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
            
            if ($key == 'Field' && $value == 'image_path') {
                $hasImagePath = true;
            }
            
            if ($key == 'Field' && $value == 'category') {
                $hasCategory = true;
            }
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    if (!$hasImagePath) {
        echo "<div style='color: red; font-weight: bold; margin-top: 10px;'>The 'image_path' column is missing from the products table!</div>";
        echo "<p>To add the column, run the following SQL query:</p>";
        echo "<pre>ALTER TABLE products ADD COLUMN image_path VARCHAR(255) DEFAULT NULL;</pre>";
    } else {
        echo "<div style='color: green; font-weight: bold; margin-top: 10px;'>The 'image_path' column exists in the products table.</div>";
    }
    
    if (!$hasCategory) {
        echo "<div style='color: red; font-weight: bold; margin-top: 10px;'>The 'category' column is missing from the products table!</div>";
        echo "<p>To add the column, run the following SQL query:</p>";
        echo "<pre>ALTER TABLE products ADD COLUMN category VARCHAR(50) DEFAULT 'General';</pre>";
    } else {
        echo "<div style='color: green; font-weight: bold; margin-top: 10px;'>The 'category' column exists in the products table.</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>Error: " . $e->getMessage() . "</div>";
}

// Check sample products and their image paths
echo "<h2>Sample Products</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM products LIMIT 10");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($products) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        foreach (array_keys($products[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
        echo "</tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            foreach ($product as $key => $value) {
                if ($key == 'image_path') {
                    echo "<td>";
                    if (!empty($value)) {
                        echo htmlspecialchars($value);
                        echo "<br><img src='" . htmlspecialchars($value) . "' style='max-width: 100px; max-height: 100px;'>";
                    } else {
                        echo "<span style='color: red;'>No image path</span>";
                    }
                    echo "</td>";
                } else {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No products found in the database.</p>";
    }
} catch (PDOException $e) {
    echo "<div style='color: red;'>Error: " . $e->getMessage() . "</div>";
}

// Add a link to go back to the admin page
echo "<p><a href='admin/products.php'>Go to Products Management</a></p>";
?>
