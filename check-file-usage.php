<?php
// Check if files are being used by the system
function checkFileUsage($filePath) {
    // Get all PHP files in the project
    $phpFiles = [];
    findPhpFiles('.', $phpFiles);
    
    $fileBaseName = basename($filePath);
    $usageCount = 0;
    $usageLocations = [];
    
    foreach ($phpFiles as $phpFile) {
        // Skip checking the file itself
        if ($phpFile == $filePath) continue;
        
        // Skip this script
        if ($phpFile == './check-file-usage.php' || $phpFile == './list-files.php') continue;
        
        $content = file_get_contents($phpFile);
        
        // Check if the file is included, required, or referenced
        if (strpos($content, $fileBaseName) !== false) {
            $usageCount++;
            $usageLocations[] = $phpFile;
        }
    }
    
    return [
        'usageCount' => $usageCount,
        'usageLocations' => $usageLocations
    ];
}

// Find all PHP files in a directory
function findPhpFiles($dir, &$phpFiles) {
    $files = scandir($dir);
    
    foreach ($files as $file) {
        if ($file == '.' || $file == '..') continue;
        
        $path = $dir . '/' . $file;
        
        if (is_dir($path)) {
            findPhpFiles($path, $phpFiles);
        } else if (pathinfo($file, PATHINFO_EXTENSION) == 'php') {
            $phpFiles[] = $path;
        }
    }
}

// Files to check
$filesToCheck = [
    './check-database.php',
    './check-image.php',
    './check-image-directory.php',
    './copy-image.php',
    './debug-image.php',
    './image-upload-test.php',
    './generate-placeholder.php',
    './db_setup.php',
    './database/db_setup.sql',
    './database/griffin_inventory.sql'
];

echo "<h1>File Usage Check</h1>";
echo "<p>Checking if files are being used by the system...</p>";

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>File</th><th>Exists</th><th>Usage Count</th><th>Usage Locations</th><th>Recommendation</th></tr>";

foreach ($filesToCheck as $file) {
    $exists = file_exists($file);
    
    if ($exists) {
        $usage = checkFileUsage($file);
        $usageCount = $usage['usageCount'];
        $usageLocations = implode("<br>", $usage['usageLocations']);
        
        if ($usageCount > 0) {
            $recommendation = "Keep (used by $usageCount file(s))";
        } else {
            $recommendation = "Safe to delete (not used by any files)";
        }
    } else {
        $usageCount = "N/A";
        $usageLocations = "N/A";
        $recommendation = "File does not exist";
    }
    
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td>" . ($exists ? "Yes" : "No") . "</td>";
    echo "<td>$usageCount</td>";
    echo "<td>$usageLocations</td>";
    echo "<td>$recommendation</td>";
    echo "</tr>";
}

echo "</table>";

// Check database files specifically
echo "<h2>Database Files Analysis</h2>";

$dbFiles = [
    './database/db_setup.sql',
    './database/griffin_inventory.sql'
];

foreach ($dbFiles as $file) {
    if (file_exists($file)) {
        echo "<h3>$file</h3>";
        
        // Get file size
        $fileSize = filesize($file);
        echo "<p>File size: " . number_format($fileSize / 1024, 2) . " KB</p>";
        
        // Check file content (first 200 characters)
        $content = file_get_contents($file);
        $preview = substr($content, 0, 200) . "...";
        echo "<p>Content preview: <pre>" . htmlspecialchars($preview) . "</pre></p>";
        
        // Check if it's referenced in importdb.php
        $importdbContent = file_exists('./importdb.php') ? file_get_contents('./importdb.php') : '';
        $isReferenced = strpos($importdbContent, basename($file)) !== false;
        echo "<p>Referenced in importdb.php: " . ($isReferenced ? "Yes" : "No") . "</p>";
    } else {
        echo "<h3>$file</h3>";
        echo "<p>File does not exist.</p>";
    }
}
?>
