<?php
$filesToCheck = [
    'check-database.php',
    'check-image.php',
    'check-image-directory.php',
    'copy-image.php',
    'debug-image.php',
    'image-upload-test.php',
    'generate-placeholder.php',
    'db_setup.php',
    'database/db_setup.sql',
    'database/griffin_inventory.sql'
];

echo "<h1>File Existence Check</h1>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>File</th><th>Exists</th></tr>";

foreach ($filesToCheck as $file) {
    $exists = file_exists($file);
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td>" . ($exists ? "Yes" : "No") . "</td>";
    echo "</tr>";
}

echo "</table>";
?>
