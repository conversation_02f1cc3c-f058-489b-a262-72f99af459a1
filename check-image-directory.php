<?php
// Debug script to check image directory permissions and create it if it doesn't exist

// Define the products directory path
$productsDir = __DIR__ . '/images/products';

echo "<h1>Image Directory Check</h1>";

// Check if images directory exists
$imagesDir = __DIR__ . '/images';
echo "<h2>Images Directory</h2>";
echo "Path: " . $imagesDir . "<br>";
echo "Exists: " . (file_exists($imagesDir) ? "Yes" : "No") . "<br>";

// Create images directory if it doesn't exist
if (!file_exists($imagesDir)) {
    echo "Creating images directory...<br>";
    $result = mkdir($imagesDir, 0777, true);
    echo "Result: " . ($result ? "Success" : "Failed") . "<br>";
    
    if ($result) {
        echo "Setting permissions...<br>";
        chmod($imagesDir, 0777);
    }
}

// Check if products directory exists
echo "<h2>Products Directory</h2>";
echo "Path: " . $productsDir . "<br>";
echo "Exists: " . (file_exists($productsDir) ? "Yes" : "No") . "<br>";

// Create products directory if it doesn't exist
if (!file_exists($productsDir)) {
    echo "Creating products directory...<br>";
    $result = mkdir($productsDir, 0777, true);
    echo "Result: " . ($result ? "Success" : "Failed") . "<br>";
    
    if ($result) {
        echo "Setting permissions...<br>";
        chmod($productsDir, 0777);
    }
}

// Check directory permissions after creation
if (file_exists($productsDir)) {
    echo "Is Directory: " . (is_dir($productsDir) ? "Yes" : "No") . "<br>";
    echo "Is Readable: " . (is_readable($productsDir) ? "Yes" : "No") . "<br>";
    echo "Is Writable: " . (is_writable($productsDir) ? "Yes" : "No") . "<br>";
    
    // Get and display directory permissions
    $perms = fileperms($productsDir);
    $info = substr(sprintf('%o', $perms), -4);
    echo "Permissions: " . $info . "<br>";
}

// Try to create a test file to verify write permissions
$testFile = $productsDir . '/test.txt';
echo "<h2>Write Test</h2>";
echo "Attempting to write test file: " . $testFile . "<br>";
$result = file_put_contents($testFile, "This is a test file to verify write permissions.");
echo "Result: " . ($result !== false ? "Success ($result bytes written)" : "Failed") . "<br>";

// If successful, remove the test file
if ($result !== false) {
    echo "Removing test file...<br>";
    $removeResult = unlink($testFile);
    echo "Result: " . ($removeResult ? "Success" : "Failed") . "<br>";
}

echo "<h2>Conclusion</h2>";
if (file_exists($productsDir) && is_writable($productsDir)) {
    echo "<div style='color: green; font-weight: bold;'>The products directory exists and is writable. Image uploads should work correctly.</div>";
} else {
    echo "<div style='color: red; font-weight: bold;'>There are issues with the products directory. Please check the permissions.</div>";
}

// Add a link to go back to the admin page
echo "<p><a href='admin/products.php'>Go to Products Management</a></p>";
?>
