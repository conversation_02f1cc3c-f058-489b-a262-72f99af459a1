<?php
// Debug script to check image paths and existence in the correct directory

// Define the product images we need
$images = [
    '13pro.jpg',
    '14pro.jpg',
    's22.jpg',
    'zfold4.jpg',
    'playstation5.jpg'
];

echo "<h1>Image Debug Information</h1>";

// Check if images/products directory exists
$productsDir = __DIR__ . '/images/products';
echo "<h2>Products Directory</h2>";
echo "Path: " . $productsDir . "<br>";
echo "Exists: " . (file_exists($productsDir) ? "Yes" : "No") . "<br>";
echo "Is Directory: " . (is_dir($productsDir) ? "Yes" : "No") . "<br>";
echo "Is Readable: " . (is_readable($productsDir) ? "Yes" : "No") . "<br>";

// List all files in the images/products directory
echo "<h2>Files in Products Directory</h2>";
if (file_exists($productsDir) && is_dir($productsDir)) {
    $files = scandir($productsDir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != "." && $file != "..") {
            echo "<li>" . $file . " (Size: " . filesize($productsDir . '/' . $file) . " bytes)</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p>Cannot list files - directory does not exist or is not readable</p>";
}

// Check each required image
echo "<h2>Required Images Status</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Image Name</th><th>Full Path</th><th>Exists</th><th>Size</th><th>Preview</th></tr>";

foreach ($images as $image) {
    $imagePath = $productsDir . '/' . $image;
    $exists = file_exists($imagePath);
    $size = $exists ? filesize($imagePath) : "N/A";
    
    echo "<tr>";
    echo "<td>" . $image . "</td>";
    echo "<td>" . $imagePath . "</td>";
    echo "<td>" . ($exists ? "Yes" : "No") . "</td>";
    echo "<td>" . $size . "</td>";
    echo "<td>";
    if ($exists) {
        echo "<img src='images/products/" . $image . "' style='max-width: 100px; max-height: 100px;'>";
    } else {
        echo "No image";
    }
    echo "</td>";
    echo "</tr>";
}

echo "</table>";

// Links to test pages
echo "<h2>Test Links</h2>";
echo "<ul>";
echo "<li><a href='storekeeper/available-products.php' target='_blank'>Available Products Page</a></li>";
echo "<li><a href='images/products/13pro.jpg' target='_blank'>Direct link to iPhone 13 Pro image</a></li>";
echo "<li><a href='images/products/14pro.jpg' target='_blank'>Direct link to iPhone 14 Pro image</a></li>";
echo "<li><a href='images/products/s22.jpg' target='_blank'>Direct link to Samsung S22 image</a></li>";
echo "<li><a href='images/products/zfold4.jpg' target='_blank'>Direct link to Z Fold 4 image</a></li>";
echo "<li><a href='images/products/playstation5.jpg' target='_blank'>Direct link to PlayStation 5 image</a></li>";
echo "</ul>";
?>
