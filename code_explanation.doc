GRIFFIN GADGETS INVENTORY SYSTEM
CODE EXPLANATION DOCUMENT

===============================================================

TABLE OF CONTENTS
----------------
1. Technologies and Languages Used
2. Directory Structure Overview
3. Core Files and Components
4. Database Structure and Relationships
5. Authentication and Security Implementation
6. User Interface Components
7. Key Functions and Algorithms
8. Integration Points
9. Reset Functionality
10. Import Database Functionality

===============================================================

1. TECHNOLOGIES AND LANGUAGES USED
---------------------------------

The Griffin Gadgets Inventory System is built using the following programming languages and technologies:

1.1 Primary Programming Languages:
   - PHP 8.x: The main server-side programming language used for all backend functionality
   - SQL: Used for database queries and data manipulation
   - HTML5: Used for structuring web pages and content
   - CSS3: Used for styling and visual presentation
   - JavaScript: Used for client-side interactivity and validation

1.2 Server-Side Technologies:
   - PHP 8.x: Powers all server-side logic, data processing, and database interactions
   - MySQL: Relational database management system for data storage
   - PDO (PHP Data Objects): Database access abstraction layer for secure database operations
   - PHP Sessions: Used for user authentication and state management

1.3 Client-Side Technologies:
   - HTML5: Structure and content markup for all pages
   - CSS3: Styling and visual presentation with responsive design
   - JavaScript: Client-side interactivity, form validation, and dynamic content
   - AJAX: Asynchronous data loading for improved user experience
   - DOM Manipulation: Dynamic HTML content updates

1.4 Frameworks and Libraries:
   - Bootstrap 5: Responsive front-end CSS framework for consistent UI
   - Font Awesome 6: Icon library for visual elements
   - Chart.js: JavaScript library for data visualization in reports
   - jQuery: JavaScript library for simplified DOM manipulation
   - DataTables: Enhanced interactive HTML tables

1.5 Development Tools:
   - XAMPP: Local development environment (Apache, MySQL, PHP)
   - Git: Version control system (if applicable)
   - Visual Studio Code/Notepad++: Code editing tools
   - Browser Developer Tools: For debugging and testing

1.6 Security Implementations:
   - Password hashing with bcrypt (PHP's password_hash function)
   - Prepared statements for SQL queries to prevent injection
   - Input validation and sanitization using PHP filters
   - Session-based authentication with secure cookies
   - CSRF protection on forms
   - Output escaping to prevent XSS attacks

===============================================================

2. DIRECTORY STRUCTURE OVERVIEW
------------------------------

The system is organized into a logical directory structure:

/INVENTORY/
├── admin/                  # Administrator interface files
├── storekeeper/            # Storekeeper interface files
├── includes/               # Shared PHP functions and components
├── images/                 # System images and product photos
│   └── products/           # Product images
├── database/               # Database schema and import files
├── css/                    # CSS stylesheets
├── js/                     # JavaScript files
├── index.php               # Main entry point/login page
├── importdb.php            # Database import utility
├── program_documentation.txt  # System documentation
├── installation_guide.txt  # Installation instructions
└── code_explanation.doc    # This document

===============================================================

3. CORE FILES AND COMPONENTS
---------------------------

3.1 Root Directory Files:

- index.php (PHP, HTML, CSS, JavaScript):
  The main entry point to the application. Handles user login and authentication.
  This file presents the login form, processes login credentials, and redirects users to their
  appropriate dashboard based on their role. Uses PHP for server-side processing, HTML/CSS for
  the login form, and JavaScript for form validation.

- importdb.php (PHP, SQL, HTML, CSS):
  A utility for one-click database setup. This file allows new installations
  to quickly set up the database without manual phpMyAdmin interaction. It creates the database,
  tables, and inserts initial sample data including default products. When moved to a new PC,
  this file will create the database structure and populate it with default products that remain
  until explicitly reset by an admin.

- adminsetup.php (PHP, HTML, CSS, JavaScript):
  Initial admin account creation page. This file allows setting up the first administrator
  account during system installation. Uses PHP for account creation, HTML/CSS for the form,
  and JavaScript for password validation.

- logout.php (PHP):
  Handles user logout by destroying the session and redirecting to the login page.
  Simple PHP script that terminates user sessions securely.

3.2 Includes Directory:

- db_connect.php (PHP, SQL):
  Establishes database connection using PDO. Contains the database credentials
  and creates a reusable connection object used throughout the application.
  Implements error handling for connection failures and database creation if needed.

- functions.php (PHP):
  Contains shared utility functions used across the application. Includes
  functions for authentication, formatting, validation, and common database operations.
  This file is the core library of reusable PHP functions that power the application.

- header.php (PHP, HTML, CSS, JavaScript):
  Common header template included in all pages. Contains the navigation menu,
  user information, and common CSS/JS includes. Dynamically adjusts navigation
  based on user role and implements the low stock alert notification system.

- footer.php (PHP, HTML):
  Common footer template included in all pages. Contains copyright information,
  contact details, and closing HTML tags. Ensures consistent page structure.

- auth.php (PHP):
  Authentication helper functions. Contains functions for login verification,
  password hashing, and session management. Implements security best practices.

3.3 Admin Directory:

- dashboard.php (PHP, HTML, CSS, JavaScript, Chart.js):
  The main admin dashboard showing key metrics, recent transactions, and alerts.
  Displays total products, stock value, low stock items, and today's sales.
  Uses Chart.js for visual data representation and JavaScript for dynamic content.

- products.php (PHP, HTML, CSS, JavaScript):
  Product management interface for adding, editing, and deleting products.
  Allows setting product details, prices, and alert levels. Includes image
  upload functionality and category management.

- add-product.php (PHP, HTML, CSS, JavaScript):
  Form for adding new products to the inventory. Handles file uploads for
  product images and validates all input fields before database insertion.

- edit-product.php (PHP, HTML, CSS, JavaScript):
  Form for modifying existing product information. Pre-populates form fields
  with current product data and handles updates to the database.

- users.php (PHP, HTML, CSS, JavaScript):
  User management interface for creating and managing user accounts.
  Handles user approval, password resets, and role assignments.
  Implements secure user management practices.

- stock-value.php (PHP, HTML, CSS, JavaScript, Chart.js):
  Detailed report of inventory value with product breakdowns.
  Shows total stock value, value by product, and includes visualization charts.
  Uses PHP for calculations and Chart.js for graphical representation.

- alerts.php (PHP, HTML, CSS):
  Displays low stock and out-of-stock products requiring attention.
  Provides quick access to restock functionality. Highlights critical items.

- transactions.php (PHP, HTML, CSS, JavaScript, DataTables):
  Complete transaction history with filtering options.
  Shows all stock movements (in/out) with user details and timestamps.
  Uses DataTables for interactive sorting and filtering.

- reset_system.php (PHP, HTML, CSS, JavaScript):
  System reset functionality for administrators. Allows clearing all products
  and transaction data after password confirmation. Implements a secure reset
  process with confirmation steps to prevent accidental data loss.

- stock-book.php (PHP, HTML, CSS, JavaScript):
  Comprehensive stock report showing opening stock, additions, removals, and
  closing stock for each product over a specified period. Includes printable
  report functionality.

3.4 Storekeeper Directory:

- dashboard.php (PHP, HTML, CSS, JavaScript):
  Storekeeper dashboard showing available products, sales, and alerts.
  Similar to admin dashboard but with limited functionality and permissions.
  Focuses on day-to-day inventory operations.

- available-products.php (PHP, HTML, CSS, JavaScript):
  Lists all products currently in stock with quantities.
  Displays product images, current stock levels, and prices.
  Provides quick access to sales and restock functions.

- out-of-stock.php (PHP, HTML, CSS):
  Shows products that have zero quantity in stock.
  Provides quick access to restock functionality.
  Helps storekeepers identify items needing replenishment.

- record-sale.php (PHP, HTML, CSS, JavaScript):
  Interface for recording product sales/stock removals.
  Updates inventory quantities and creates transaction records.
  Includes customer information capture and receipt generation.

- restock.php (PHP, HTML, CSS, JavaScript):
  Interface for adding inventory to existing products.
  Updates product quantities and creates transaction records.
  Includes supplier information and batch tracking.

- stock-book.php (PHP, HTML, CSS, JavaScript):
  Storekeeper version of the stock report with more limited options.
  Allows viewing but not modifying historical stock data.

3.5 CSS Directory:

- bootstrap.min.css (CSS):
  Minified Bootstrap 5 framework for responsive design and UI components.

- all.min.css (CSS):
  Font Awesome 6 icon library for visual elements throughout the application.

- styles.css (CSS):
  Custom stylesheet with application-specific styles and overrides.
  Implements the color scheme, custom components, and responsive adjustments.

- print.css (CSS):
  Print-specific styles for reports and receipts. Optimizes output for printed media.

3.6 JavaScript Directory:

- bootstrap.bundle.min.js (JavaScript):
  Minified Bootstrap 5 JavaScript bundle including Popper.js for UI components.

- jquery.min.js (JavaScript):
  jQuery library for simplified DOM manipulation and AJAX functionality.

- chart.min.js (JavaScript):
  Chart.js library for data visualization in dashboard and reports.

- datatables.min.js (JavaScript):
  DataTables library for enhanced interactive HTML tables with sorting and filtering.

- password-toggle.js (JavaScript):
  Custom script for password visibility toggle on login and user forms.

- validation.js (JavaScript):
  Form validation scripts for client-side input validation.

- dashboard.js (JavaScript):
  Custom scripts for dashboard functionality including charts and real-time updates.

3.7 Images Directory:

- logo.jpg:
  Company logo used in header and login page.

- products/ (Directory):
  Contains all product images organized by product ID.
  Images are uploaded through the product management interface.

3.8 Database Directory:

- griffin_inventory.sql (SQL):
  Complete database schema with table definitions, relationships, and initial data.
  Used by importdb.php for database setup.

===============================================================

4. DATABASE STRUCTURE AND RELATIONSHIPS
--------------------------------------

The database is implemented in MySQL and consists of five primary tables with well-defined relationships. All SQL operations are performed using PHP's PDO extension with prepared statements for security.

4.1 users Table (SQL):
   - Primary key: user_id (INT, auto-increment)
   - username (VARCHAR(50), unique) - Login identifier
   - password (VARCHAR(255)) - Bcrypt hashed password
   - full_name (VARCHAR(100)) - User's full name
   - email (VARCHAR(100)) - User's email address
   - role (ENUM('admin', 'storekeeper')) - User permission level
   - status (ENUM('active', 'inactive', 'pending')) - Account status
   - password_reset_requested (BOOLEAN) - Flag for password reset
   - created_at (TIMESTAMP) - Account creation timestamp
   - last_login (TIMESTAMP) - Last successful login

4.2 categories Table (SQL):
   - Primary key: category_id (INT, auto-increment)
   - category_name (VARCHAR(50), unique) - Category name
   - created_at (TIMESTAMP) - Category creation timestamp
   - Default categories include: Phones, Laptops, Accessories, Tablets, Wearables, Gaming

4.3 products Table (SQL):
   - Primary key: product_id (INT, auto-increment)
   - product_name (VARCHAR(100)) - Product name
   - description (TEXT) - Detailed product description
   - quantity (INT) - Current stock quantity
   - price (DECIMAL(12,2)) - Product price in Nigerian Naira
   - alert_level (INT) - Threshold for low stock alerts
   - image_path (VARCHAR(255)) - Path to product image
   - category (VARCHAR(50)) - Product category (references categories.category_name)
   - created_at (TIMESTAMP) - Product creation timestamp
   - updated_at (TIMESTAMP) - Last update timestamp

4.4 stock_transactions Table (SQL):
   - Primary key: transaction_id (INT, auto-increment)
   - Foreign key: product_id (INT) - References products.product_id
   - Foreign key: user_id (INT) - References users.user_id
   - quantity (INT) - Transaction quantity
   - transaction_type (ENUM('in', 'out')) - Stock addition or removal
   - notes (TEXT) - Transaction details or reason
   - transaction_date (TIMESTAMP) - When transaction occurred
   - ON DELETE constraints ensure referential integrity

4.5 sales Table (SQL):
   - Primary key: sale_id (INT, auto-increment)
   - Foreign key: product_id (INT) - References products.product_id
   - Foreign key: user_id (INT) - References users.user_id
   - quantity (INT) - Quantity sold
   - sale_price (DECIMAL(12,2)) - Price at time of sale
   - customer_name (VARCHAR(100)) - Customer information
   - customer_phone (VARCHAR(20)) - Customer contact
   - sale_date (TIMESTAMP) - When sale occurred
   - ON DELETE constraints ensure referential integrity

4.6 system_logs Table (SQL):
   - Primary key: log_id (INT, auto-increment)
   - Foreign key: user_id (INT) - References users.user_id (nullable)
   - action_type (VARCHAR(50)) - Type of system action
   - description (TEXT) - Detailed description of action
   - ip_address (VARCHAR(45)) - IP address of user
   - log_date (TIMESTAMP) - When action occurred
   - ON DELETE SET NULL allows user deletion while preserving logs

4.7 Relationships:
   - One user can perform many transactions (one-to-many)
   - One user can record many sales (one-to-many)
   - One user can generate many system logs (one-to-many)
   - One product can have many transactions (one-to-many)
   - One product can have many sales (one-to-many)
   - One category can have many products (one-to-many)
   - Each transaction is associated with exactly one user and one product
   - Each sale is associated with exactly one user and one product

4.8 Database Operations:
   - All database operations use PHP PDO with prepared statements
   - Foreign key constraints ensure data integrity
   - Transactions are used for operations affecting multiple tables
   - Indexes are created on frequently queried columns
   - Timestamps track creation and modification times
   - ENUM types enforce valid values for status fields

===============================================================

5. AUTHENTICATION AND SECURITY IMPLEMENTATION
-------------------------------------------

5.1 Authentication Flow:

   1. User enters credentials on index.php login form
   2. Credentials are validated against the users table
   3. Password is verified using password_verify() against stored hash
   4. On successful authentication, user role is checked
   5. Session variables are set with user information
   6. User is redirected to appropriate dashboard based on role

5.2 Security Measures:

   - Passwords are hashed using PHP's password_hash() with bcrypt
   - All database queries use prepared statements to prevent SQL injection
   - Input data is validated and sanitized before processing
   - Output data is escaped to prevent XSS attacks
   - Role-based access control restricts page access
   - Session timeout for inactive users
   - CSRF protection on forms

5.3 Access Control Functions:

   - requireLogin(): Ensures user is authenticated
   - requireAdmin(): Restricts access to admin users only
   - requireStorekeeper(): Restricts access to storekeeper users only
   - checkPermission(): Verifies specific permission for current user

===============================================================

6. USER INTERFACE COMPONENTS
---------------------------

6.1 Dashboard Cards:

   The dashboard uses card components to display key metrics. Each card shows:
   - Icon representing the metric
   - Title describing the metric
   - Value (count, amount, etc.)
   - Color coding for visual differentiation
   - Click functionality to navigate to related pages

6.2 Data Tables:

   Tables are used throughout the system to display lists of data:
   - Responsive design that works on all screen sizes
   - Sortable columns where appropriate
   - Search/filter functionality
   - Pagination for large datasets
   - Action buttons for row-specific operations

6.3 Forms:

   Forms follow a consistent pattern throughout the system:
   - Clear labeling and grouping of related fields
   - Client-side validation with JavaScript
   - Server-side validation as a backup
   - Error messages displayed near relevant fields
   - Consistent button styling and positioning

6.4 Alerts and Notifications:

   The system uses several types of alerts:
   - Success messages (green) for completed actions
   - Warning messages (yellow) for potential issues
   - Error messages (red) for failed operations
   - Info messages (blue) for general information
   - Dismissible alerts with close buttons

===============================================================

7. KEY FUNCTIONS AND ALGORITHMS
------------------------------

7.1 Stock Management:

   - addStock(): Increases product quantity and logs transaction
   - removeStock(): Decreases product quantity and logs transaction
   - checkStockLevel(): Verifies if product is at or below alert level
   - calculateStockValue(): Computes total value of inventory

7.2 User Management:

   - createUser(): Adds new user to the system
   - approveUser(): Changes user status from pending to active
   - resetPassword(): Handles password reset requests
   - updateUserRole(): Changes user permission level

7.3 Reporting:

   - getRecentTransactions(): Retrieves latest stock movements
   - getLowStockProducts(): Identifies products needing attention
   - calculateSales(): Computes sales for a given time period
   - generateStockReport(): Creates comprehensive inventory report

7.4 Utility Functions:

   - formatCurrency(): Displays monetary values in Nigerian Naira format
   - sanitizeInput(): Cleans user input to prevent security issues
   - validateForm(): Checks form submissions for required fields
   - generatePagination(): Creates page navigation for large datasets

===============================================================

8. INTEGRATION POINTS
--------------------

8.1 External Libraries:

   - Bootstrap: Integrated via CDN for responsive design
   - Font Awesome: Integrated via CDN for icons
   - Chart.js: Integrated via CDN for data visualization

8.2 Database Connection:

   - PDO connection established in db_connect.php
   - Connection parameters can be modified for different environments
   - Error handling for failed connections

8.3 Image Handling:

   - Product images stored in images/products/ directory
   - Image paths stored in database or determined programmatically
   - Standard image sizes maintained for consistency

8.4 Session Management:

   - PHP sessions used for maintaining user state
   - Session variables store user ID, name, role
   - Session timeout configured for security

===============================================================

9. RESET FUNCTIONALITY
---------------------

The system includes a comprehensive reset functionality that allows administrators to clear all products and transaction data while maintaining the system structure and user accounts.

9.1 Implementation Files:

- admin/reset_system.php (PHP, HTML, CSS, JavaScript):
  The main file implementing the reset functionality. This file handles the reset process
  including user authentication, confirmation, and database operations.

- admin/dashboard.php (PHP, HTML, CSS, JavaScript):
  Contains the reset button and confirmation modal that triggers the reset process.
  Implements a multi-step confirmation process to prevent accidental resets.

9.2 Reset Process Flow:

1. Administrator clicks the "Reset System" button on the dashboard
2. A confirmation modal appears with warnings about data loss
3. Administrator must check a confirmation checkbox acknowledging the consequences
4. Administrator must enter their password for security verification
5. Upon submission, the system verifies the password
6. If verified, the system:
   - Temporarily disables foreign key checks
   - Truncates the products table (completely removes all products)
   - Truncates the stock_transactions table (removes all transaction history)
   - Truncates the sales table (removes all sales records)
   - Re-enables foreign key checks
   - Maintains categories and user accounts
7. A success message is displayed with details of the reset operation
8. The action is logged in the system_logs table for audit purposes

9.3 Security Measures:

- Password verification ensures only authorized administrators can perform resets
- Confirmation checkbox prevents accidental submissions
- Clear warnings about the irreversible nature of the action
- Transaction-safe database operations to prevent partial resets
- Comprehensive error handling with detailed error messages
- Audit logging of all reset actions with user identification

9.4 Technical Implementation:

- Uses PHP PDO for database operations with prepared statements
- Implements table existence checks before operations to prevent errors
- Uses TRUNCATE TABLE instead of DELETE for efficient complete removal
- Handles foreign key constraints properly to maintain database integrity
- Implements separate error handling for each operation
- Uses Bootstrap modal for the confirmation interface

10. IMPORT DATABASE FUNCTIONALITY
------------------------------

The importdb.php file provides a one-click database setup solution that creates the database structure and populates it with initial data when setting up the system on a new PC.

10.1 Key Features:

- Creates the complete database structure from scratch
- Populates the database with default categories
- Adds default products that remain until explicitly reset
- Creates sample user accounts for testing
- Generates initial stock transactions
- Provides a user-friendly interface with progress feedback
- Handles error conditions gracefully

10.2 Implementation Details:

- Written in PHP with MySQL database operations
- Uses mysqli for direct database connection and operations
- Implements SQL file parsing and execution
- Creates necessary directories and files if they don't exist
- Generates default JavaScript files for password toggling
- Creates admin and storekeeper interface files

10.3 Default Products Creation:

- After database creation, checks if products table is empty
- If empty, inserts a set of default products (iPhones, Samsung, etc.)
- Creates corresponding stock transactions for these products
- Links transactions to admin user for proper record-keeping
- These products remain in the system until explicitly reset by an admin

10.4 Process Flow:

1. User accesses importdb.php in a browser
2. User clicks "Import Database Now" button
3. System checks for existing database and creates it if needed
4. System creates all required tables with proper relationships
5. System populates tables with default data including products
6. System creates necessary directories and files
7. System displays success message with next steps
8. User proceeds to admin account setup

10.5 Technical Implementation:

- Uses mysqli for database operations
- Implements foreign key constraint management
- Handles SQL file parsing and chunked execution
- Provides detailed logging of all operations
- Implements error handling with user-friendly messages
- Creates a complete working system in a single operation

===============================================================

CONCLUSION
----------

The Griffin Gadgets Inventory System is built with a focus on modularity,
security, and user experience. The code is structured to be maintainable
and extensible, with clear separation of concerns between different
components. The system leverages modern web technologies while maintaining
compatibility with standard hosting environments.

The primary programming languages used are PHP for server-side logic, SQL for
database operations, HTML/CSS for presentation, and JavaScript for client-side
interactivity. The system uses the Bootstrap framework for responsive design
and various JavaScript libraries for enhanced functionality.

Key features include comprehensive product management, transaction tracking,
user role-based access control, reporting capabilities, and system maintenance
tools like the reset functionality and database import utility.

This document provides a high-level overview of the code structure and
implementation. For more detailed information about specific functions
or components, refer to the inline code comments or contact the
development team.

===============================================================
