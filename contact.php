<?php
// Include header
include 'includes/header.php';
?>

<div class="container mt-3 mb-4">
    <div class="card shadow">
        <div class="card-body p-3 p-md-4">
            <h1 class="text-center mb-3">Contact Us</h1>
            <p class="text-center mb-3">Get in touch with us for any questions or support</p>

            <div class="row">
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-body p-3">
                            <h3 class="card-title mb-3">Contact Information</h3>

                            <div class="contact-social-row">
                                <div class="contact-social-icon" style="background-color: #007bff;">
                                    <i class="fas fa-phone fa-2x"></i>
                                </div>
                                <div class="contact-social-text">
                                    <h5>Phone</h5>
                                    <p>+234-************</p>
                                </div>
                            </div>

                            <div class="contact-social-row">
                                <div class="contact-social-icon" style="background-color: #25D366;">
                                    <i class="fab fa-whatsapp fa-2x"></i>
                                </div>
                                <div class="contact-social-text">
                                    <h5>WhatsApp</h5>
                                    <p>+234-************</p>
                                    <a href="https://wa.link/zx4hlz" target="_blank" style="color: #25D366; text-decoration: none;">wa.link/zx4hlz</a>
                                </div>
                            </div>

                            <div class="contact-social-row">
                                <div class="contact-social-icon" style="background-color: #EA4335;">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                                <div class="contact-social-text">
                                    <h5>Email</h5>
                                    <p><EMAIL></p>
                                </div>
                            </div>

                            <h3 class="mt-4 mb-3">Connect With Us</h3>

                            <div class="social-icons-container">
                                <a href="https://web.facebook.com/eberechukwu.joseph3?rdid=ryClfdfRuJoGbjUe&share_url=https%3A%2F%2Fweb.facebook.com%2Fshare%2F1A7BL2BFQQ%2F%3F_rdc%3D1%26_rdr#" class="social-circle facebook-bg" target="_blank">
                                    <i class="fab fa-facebook-f fa-lg"></i>
                                </a>
                                <a href="https://x.com/Genuine_Art1?t=ZLN7A43h0zDNIpxSSE2kjA&s=09" class="social-circle twitter-bg" target="_blank">
                                    <i class="fab fa-twitter fa-lg"></i>
                                </a>
                                <a href="https://www.instagram.com/genuine_art1/?utm_source=qr&igsh=aHJ6OHBhbHJzOXNj#" class="social-circle instagram-bg" target="_blank">
                                    <i class="fab fa-instagram fa-lg"></i>
                                </a>
                                <a href="https://www.linkedin.com/in/eberechukwu-joseph/" class="social-circle linkedin-bg" target="_blank">
                                    <i class="fab fa-linkedin-in fa-lg"></i>
                                </a>
                            </div>

                            <style>
                            .contact-social-row {
                                display: flex;
                                align-items: center;
                                margin-bottom: 15px;
                            }

                            .contact-social-icon {
                                width: 50px;
                                height: 50px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-right: 15px;
                                flex-shrink: 0;
                            }

                            .contact-social-text h5 {
                                margin-bottom: 0;
                                font-size: 16px;
                            }

                            .contact-social-text p {
                                margin-bottom: 0;
                                color: #6c757d;
                                font-size: 14px;
                            }

                            /* Horizontal social icons */
                            .social-icons-container {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: 15px;
                                width: 100%;
                            }

                            @keyframes socialIconPulse {
                                0% { transform: scale(1); }
                                50% { transform: scale(1.05); }
                                100% { transform: scale(1); }
                            }

                            .social-circle {
                                width: 50px;
                                height: 50px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                text-decoration: none;
                                transition: all 0.3s ease;
                                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                                position: relative;
                                overflow: hidden;
                            }

                            .social-circle::before {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: rgba(255, 255, 255, 0.1);
                                transform: translateX(-100%);
                                transition: transform 0.6s ease;
                            }

                            .social-circle:hover::before {
                                transform: translateX(0);
                            }

                            .social-circle:hover {
                                transform: translateY(-5px) scale(1.1);
                                box-shadow: 0 8px 15px rgba(0,0,0,0.2);
                            }

                            .facebook-bg {
                                background-color: #3b5998;
                            }

                            .twitter-bg {
                                background-color: #1da1f2;
                            }

                            .instagram-bg {
                                background: linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
                            }

                            .linkedin-bg {
                                background-color: #0077b5;
                            }

                            @media (max-width: 768px) {
                                .contact-social-icon, .social-circle {
                                    width: 45px;
                                    height: 45px;
                                }

                                .social-icons-container {
                                    justify-content: space-around;
                                    padding: 0 10px;
                                }
                            }

                            @media (max-width: 480px) {
                                .social-icons-container {
                                    width: 100%;
                                    justify-content: space-between;
                                    padding: 0;
                                }

                                .social-circle {
                                    width: 40px;
                                    height: 40px;
                                    font-size: 0.9em;
                                }
                            }
                            </style>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-body p-3">
                            <h3 class="card-title mb-3">Our Location</h3>
                            <div class="embed-responsive embed-responsive-16by9">
                                <iframe class="embed-responsive-item" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63520.90356508145!2d7.3442023!3d5.1067331!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x10429a146f57e18f%3A0x684c2eadf37d2953!2sAba%2C%20Abia!5e0!3m2!1sen!2sng!4v1623456789012!5m2!1sen!2sng" allowfullscreen="" loading="lazy"></iframe>
                            </div>
                            <div class="mt-3">
                                <h5 class="mb-1">Address</h5>
                                <p class="mb-0">Aba, Abia State, Nigeria</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script src="js/contact.js"></script>

<?php
// Include footer
include 'includes/footer.php';
?>
