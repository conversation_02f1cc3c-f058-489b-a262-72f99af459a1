<?php
// This script copies the required images to the images directory

// Define the required images
$requiredImages = [
    '13pro.jpg',
    '14pro.jpg',
    's22.jpg',
    'zfold4.jpg',
    'playstation5.jpg'
];

echo "<h1>Copy Required Images</h1>";

// Create the images directory if it doesn't exist
$imagesDir = __DIR__ . '/images';
if (!file_exists($imagesDir)) {
    mkdir($imagesDir, 0777, true);
    echo "<p>Created images directory</p>";
}

// Create each image
foreach ($requiredImages as $imageName) {
    $imagePath = $imagesDir . '/' . $imageName;
    
    // Create a simple colored image
    $width = 300;
    $height = 300;
    $im = imagecreatetruecolor($width, $height);
    
    // Set a different color for each product type
    switch ($imageName) {
        case '13pro.jpg':
        case '14pro.jpg':
            // Blue for iPhones
            $bgColor = imagecolorallocate($im, 0, 122, 255);
            break;
        case 's22.jpg':
        case 'zfold4.jpg':
            // Green for Samsung
            $bgColor = imagecolorallocate($im, 76, 175, 80);
            break;
        case 'playstation5.jpg':
            // Black for PlayStation
            $bgColor = imagecolorallocate($im, 33, 33, 33);
            break;
        default:
            // Gray for others
            $bgColor = imagecolorallocate($im, 158, 158, 158);
    }
    
    // Fill the image with the background color
    imagefill($im, 0, 0, $bgColor);
    
    // Add product name text
    $textColor = imagecolorallocate($im, 255, 255, 255);
    $productName = str_replace('.jpg', '', $imageName);
    
    // Format the product name for display
    switch ($productName) {
        case '13pro':
            $displayName = 'iPhone 13 Pro';
            break;
        case '14pro':
            $displayName = 'iPhone 14 Pro';
            break;
        case 's22':
            $displayName = 'Samsung S22';
            break;
        case 'zfold4':
            $displayName = 'Z Fold 4';
            break;
        case 'playstation5':
            $displayName = 'PlayStation 5';
            break;
        default:
            $displayName = $productName;
    }
    
    // Center the text
    $fontSize = 5;
    $textWidth = imagefontwidth($fontSize) * strlen($displayName);
    $textX = ($width - $textWidth) / 2;
    $textY = $height / 2 - imagefontheight($fontSize) / 2;
    
    imagestring($im, $fontSize, $textX, $textY, $displayName, $textColor);
    
    // Save the image
    imagejpeg($im, $imagePath, 90);
    imagedestroy($im);
    
    echo "<p>Created image: {$imageName} at {$imagePath}</p>";
}

echo "<p>All images created successfully!</p>";
echo "<p><a href='storekeeper/available-products.php'>Go to Available Products</a></p>";
?>
