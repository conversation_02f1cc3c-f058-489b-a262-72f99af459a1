/* Main Styles for Griffin Gadgets Inventory Management System */
/* Updated for cross-platform compatibility (Windows 10/11) */

:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #f0f5fa;
    --dark-color: #2c3e50;
    --text-color: #333;
    --text-muted: #7f8c8d;
    --border-color: #ddd;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

/* General Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--text-color);
    background-color: #f0f5fa;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-speed) ease;
}

a:hover {
    color: #217dbb;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #217dbb;
    border-color: #217dbb;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #1a252f;
    border-color: #1a252f;
}

.btn-accent {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.btn-accent:hover {
    background-color: #c0392b;
    border-color: #c0392b;
    color: white;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
    background-color: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    height: 100%;
    cursor: pointer;
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

a.text-decoration-none:hover {
    text-decoration: none;
}

.dashboard-card .icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.dashboard-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-muted);
}

.dashboard-card .card-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

.dashboard-card.primary {
    background-color: var(--primary-color);
    color: white;
}

.dashboard-card.secondary {
    background-color: var(--secondary-color);
    color: white;
}

.dashboard-card.success {
    background-color: var(--success-color);
    color: white;
}

.dashboard-card.warning {
    background-color: var(--warning-color);
    color: white;
}

.dashboard-card.danger {
    background-color: var(--danger-color);
    color: white;
}

/* Tables */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table th {
    background-color: var(--secondary-color);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* Forms */
.form-control {
    border-radius: 6px;
    border: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: border-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

/* Login Page */
.login-container {
    max-width: 400px;
    margin: 2rem auto;
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.login-logo img {
    height: 80px;
    border: 3px solid white;
    border-radius: 50%;
    padding: 5px;
}

/* Product Cards */
.product-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    height: 100%;
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.product-card .card-img-top {
    height: 200px;
    object-fit: contain;
    padding: 10px;
    background-color: #fff;
}

.product-card .card-body {
    padding: 1.5rem;
}

.product-card .card-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.product-card .card-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-card .card-stock {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

/* Alerts */
.alert-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: 1rem;
}

.alert-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.alert-icon.warning {
    background-color: var(--warning-color);
    color: white;
}

.alert-icon.danger {
    background-color: var(--danger-color);
    color: white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-card .card-value {
        font-size: 1.5rem;
    }

    .dashboard-card .icon {
        font-size: 2rem;
    }

    .table th, .table td {
        padding: 0.5rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Product Images */
.product-image {
    width: 50px;
    height: 50px;
    object-fit: contain;
    padding: 3px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease;
}

.product-image:hover {
    transform: scale(1.1);
}

/* Larger product images in product cards */
.product-card .card-img-top {
    max-height: 200px;
    width: auto;
    max-width: 100%;
    margin: 0 auto;
    display: block;
}

/* Custom Scrollbar - with fallbacks for different browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #217dbb;
}

/* Cross-browser compatibility fixes */
input, button, select, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* Fix for inconsistent button rendering */
button {
    cursor: pointer;
    background-color: transparent;
    border: none;
}

/* Fix for image display */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Print styles for reports */
@media print {
    body {
        background-color: white;
        color: black;
    }

    .no-print {
        display: none !important;
    }

    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
