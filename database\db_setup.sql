-- Create database
CREATE DATABASE IF NOT EXISTS griffin_inventory;
USE griffin_inventory;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    role ENUM('admin', 'storekeeper') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    product_id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    quantity INT NOT NULL DEFAULT 0,
    alert_level INT NOT NULL DEFAULT 5,
    image_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP
);

-- Create stock_transactions table
CREATE TABLE IF NOT EXISTS stock_transactions (
    transaction_id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    transaction_type ENUM('in', 'out') NOT NULL,
    quantity INT NOT NULL,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, full_name, email, role)
VALUES ('admin', '$2y$10$8WxmVFNDrgEYxFLs.JYxS.vzYP.utRq9Pg8xNxk.Mb9EXh6xQYvnC', 'System Administrator', '<EMAIL>', 'admin');

-- Insert default storekeeper user (password: store123)
INSERT INTO users (username, password, full_name, email, role)
VALUES ('storekeeper', '$2y$10$Qs.vK7LZ9D.DkVhLQQ9Cg.9VhQMJRcKvTvSKZK1XAqSL5JBq9cVoO', 'Store Keeper', '<EMAIL>', 'storekeeper');

-- Insert sample products
INSERT INTO products (product_name, category, description, price, quantity, alert_level, image_path) VALUES
('iPhone 13 Pro', 'Phones', '6.1-inch Super Retina XDR display with ProMotion, A15 Bionic chip', 999.99, 15, 5, 'images/products/iphone13pro.jpg'),
('iPhone 14', 'Phones', 'Latest iPhone with improved camera and performance', 799.99, 20, 8, 'images/products/iphone14.jpg'),
('Samsung Galaxy S22', 'Phones', 'Dynamic AMOLED 2X display, 120Hz refresh rate', 749.99, 18, 6, 'images/products/galaxys22.jpg'),
('Samsung Galaxy Z Fold 4', 'Phones', 'Foldable smartphone with large display', 1799.99, 8, 3, 'images/products/zfold4.jpg'),
('PlayStation 5', 'Gaming', 'Next-gen gaming console with ultra-high-speed SSD', 499.99, 10, 4, 'images/products/ps5.jpg');
