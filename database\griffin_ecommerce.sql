-- Griffin Gadgets E-Commerce Platform Database Schema
-- Enhanced from inventory system to full e-commerce functionality

-- Drop database if it exists and create a new one
DROP DATABASE IF EXISTS griffin_ecommerce;
CREATE DATABASE griffin_ecommerce;
USE griffin_ecommerce;

-- Create users table with enhanced roles and security questions
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20) DEFAULT NULL,
    role ENUM('admin', 'sales_rep', 'customer') NOT NULL,
    status ENUM('active', 'inactive', 'pending') NOT NULL DEFAULT 'pending',
    security_question_1 TEXT DEFAULT NULL,
    security_answer_1 VARCHAR(255) DEFAULT NULL,
    security_question_2 TEXT DEFAULT NULL,
    security_answer_2 VARCHAR(255) DEFAULT NULL,
    address TEXT DEFAULT NULL,
    password_reset_requested BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    approved_by INT DEFAULT NULL,
    approved_at TIMESTAMP NULL,
    FOREIGN KEY (approved_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Create categories table for better product organization
CREATE TABLE categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT DEFAULT NULL,
    image_path VARCHAR(255) DEFAULT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create products table with enhanced e-commerce features
CREATE TABLE products (
    product_id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(200) NOT NULL,
    description TEXT,
    short_description VARCHAR(500) DEFAULT NULL,
    category_id INT DEFAULT NULL,
    price DECIMAL(12, 2) NOT NULL,
    compare_price DECIMAL(12, 2) DEFAULT NULL,
    cost_price DECIMAL(12, 2) DEFAULT NULL,
    quantity INT NOT NULL DEFAULT 0,
    alert_level INT NOT NULL DEFAULT 5,
    sku VARCHAR(100) UNIQUE DEFAULT NULL,
    barcode VARCHAR(100) DEFAULT NULL,
    weight DECIMAL(8, 2) DEFAULT NULL,
    dimensions VARCHAR(100) DEFAULT NULL,
    image_path VARCHAR(255) DEFAULT NULL,
    gallery_images JSON DEFAULT NULL,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    meta_title VARCHAR(200) DEFAULT NULL,
    meta_description TEXT DEFAULT NULL,
    tags TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT DEFAULT NULL,
    FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Create shopping cart table
CREATE TABLE shopping_cart (
    cart_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(12, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_product (user_id, product_id)
);

-- Create orders table
CREATE TABLE orders (
    order_id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INT NOT NULL,
    sales_rep_id INT DEFAULT NULL,
    order_status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50) DEFAULT NULL,
    payment_reference VARCHAR(100) DEFAULT NULL,
    subtotal DECIMAL(12, 2) NOT NULL,
    tax_amount DECIMAL(12, 2) DEFAULT 0.00,
    shipping_amount DECIMAL(12, 2) DEFAULT 0.00,
    discount_amount DECIMAL(12, 2) DEFAULT 0.00,
    total_amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'NGN',
    customer_notes TEXT DEFAULT NULL,
    admin_notes TEXT DEFAULT NULL,
    shipping_address TEXT DEFAULT NULL,
    billing_address TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    shipped_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    FOREIGN KEY (customer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (sales_rep_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Create order items table
CREATE TABLE order_items (
    item_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_sku VARCHAR(100) DEFAULT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(12, 2) NOT NULL,
    total_price DECIMAL(12, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Create stock transactions table (enhanced from inventory system)
CREATE TABLE stock_transactions (
    transaction_id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    order_id INT DEFAULT NULL,
    quantity INT NOT NULL,
    transaction_type ENUM('in', 'out', 'adjustment') NOT NULL,
    reason ENUM('purchase', 'sale', 'return', 'damage', 'adjustment', 'initial_stock') DEFAULT 'adjustment',
    notes TEXT DEFAULT NULL,
    reference_number VARCHAR(100) DEFAULT NULL,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE SET NULL
);

-- Create payment transactions table
CREATE TABLE payment_transactions (
    transaction_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    payment_gateway VARCHAR(50) NOT NULL,
    gateway_transaction_id VARCHAR(100) DEFAULT NULL,
    gateway_reference VARCHAR(100) DEFAULT NULL,
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'NGN',
    status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending',
    gateway_response JSON DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE
);

-- Create system logs table
CREATE TABLE system_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    action_type VARCHAR(50) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    log_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Insert default categories
INSERT INTO categories (category_name, description) VALUES
('Smartphones', 'Latest smartphones and mobile devices'),
('Gaming', 'Gaming consoles and accessories'),
('Accessories', 'Phone accessories and gadgets');

-- Insert initial products (as specified in requirements)
INSERT INTO products (product_name, description, short_description, category_id, price, quantity, alert_level, image_path, sku, featured, status) VALUES
('iPhone 13 Pro', 'Apple iPhone 13 Pro with advanced camera system, A15 Bionic chip, and ProMotion display. Available in multiple colors with premium build quality and exceptional performance for professional photography and videography.', 'Latest iPhone 13 Pro with advanced features', 1, 1700000.00, 10, 3, 'images/products/13pro.jpg', 'IP13PRO-128', TRUE, 'active'),
('iPhone 14 Pro', 'Apple iPhone 14 Pro featuring the powerful A16 Bionic chip, Dynamic Island, and professional camera system for stunning photos and videos. Experience the future of iPhone with innovative design and cutting-edge technology.', 'Cutting-edge iPhone 14 Pro with Dynamic Island', 1, 2400000.00, 8, 3, 'images/products/14pro.jpg', 'IP14PRO-128', TRUE, 'active'),
('iPhone 15 Pro', 'Apple iPhone 15 Pro with titanium design, A17 Pro chip, and USB-C connectivity. The most advanced iPhone with pro-level performance, enhanced durability, and revolutionary features for the ultimate mobile experience.', 'Revolutionary iPhone 15 Pro with titanium build', 1, 2600000.00, 5, 2, 'images/products/15pro.jpg', 'IP15PRO-128', TRUE, 'active');

-- Note: Admin user will be created through the direct-reset.php script
-- Sample customer will be created during testing
