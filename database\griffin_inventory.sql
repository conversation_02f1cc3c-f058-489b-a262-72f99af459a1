-- Griffin Gadgets Inventory System Database Schema
-- This file contains the complete database structure for the inventory system

-- Drop database if it exists and create a new one
DROP DATABASE IF EXISTS griffin_inventory;
CREATE DATABASE griffin_inventory;
USE griffin_inventory;

-- Create users table
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    role ENUM('admin', 'storekeeper') NOT NULL,
    status ENUM('active', 'inactive', 'pending') NOT NULL DEFAULT 'pending',
    password_reset_requested BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Create products table
CREATE TABLE products (
    product_id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    description TEXT,
    quantity INT NOT NULL DEFAULT 0,
    price DECIMAL(12, 2) NOT NULL,
    alert_level INT NOT NULL DEFAULT 5,
    image_path VARCHAR(255) DEFAULT NULL,
    category VARCHAR(50) DEFAULT 'General',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create stock_transactions table
CREATE TABLE stock_transactions (
    transaction_id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    quantity INT NOT NULL,
    transaction_type ENUM('in', 'out') NOT NULL,
    notes TEXT,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Admin user will be created through the adminsetup.php page

-- Insert sample products
INSERT INTO products (product_name, description, quantity, price, alert_level, image_path, category) VALUES
('iPhone 13 Pro', 'Apple iPhone 13 Pro 128GB', 15, 550000.00, 5, 'images/products/13pro.jpg', 'Phones'),
('iPhone 14', 'Apple iPhone 14 256GB', 10, 650000.00, 3, 'images/products/14pro.jpg', 'Phones'),
('Samsung Galaxy S22', 'Samsung Galaxy S22 Ultra 256GB', 8, 480000.00, 3, 'images/products/s22.jpg', 'Phones'),
('Samsung Galaxy Z Fold 4', 'Samsung Galaxy Z Fold 4 512GB', 5, 750000.00, 2, 'images/products/zfold4.jpg', 'Phones'),
('PlayStation 5', 'Sony PlayStation 5 Digital Edition', 7, 350000.00, 3, 'images/products/playstation5.jpg', 'Gaming');

-- Insert sample storekeeper (username: storekeeper, password: store123)
INSERT INTO users (username, password, full_name, email, role, status)
VALUES ('marcus', '$2y$10$8WxhXQQBZuEYl0H0bxpEbOUUQHX5KGXEQdmnqvfK7NuQm2JC8Lfwi', 'MARCUS', '<EMAIL>', 'storekeeper', 'active');

-- Insert sample admin user (username: admin, password: admin123) for initial transactions
-- This will be replaced by the actual admin setup process
INSERT INTO users (username, password, full_name, email, role, status)
VALUES ('temp_admin', '$2y$10$8WxhXQQBZuEYl0H0bxpEbOUUQHX5KGXEQdmnqvfK7NuQm2JC8Lfwi', 'Temporary Admin', '<EMAIL>', 'admin', 'active');

-- Insert sample transactions
-- Using subqueries to get the actual user_id values
INSERT INTO stock_transactions (product_id, user_id, quantity, transaction_type, notes) VALUES
(1, (SELECT user_id FROM users WHERE username = 'temp_admin'), 20, 'in', 'Initial stock'),
(2, (SELECT user_id FROM users WHERE username = 'temp_admin'), 15, 'in', 'Initial stock'),
(3, (SELECT user_id FROM users WHERE username = 'temp_admin'), 10, 'in', 'Initial stock'),
(4, (SELECT user_id FROM users WHERE username = 'temp_admin'), 8, 'in', 'Initial stock'),
(5, (SELECT user_id FROM users WHERE username = 'temp_admin'), 10, 'in', 'Initial stock'),
(1, (SELECT user_id FROM users WHERE username = 'marcus'), 5, 'out', 'Sold to customer'),
(2, (SELECT user_id FROM users WHERE username = 'marcus'), 3, 'out', 'Sold to customer'),
(3, (SELECT user_id FROM users WHERE username = 'marcus'), 2, 'out', 'Sold to customer'),
(4, (SELECT user_id FROM users WHERE username = 'marcus'), 3, 'out', 'Sold to customer'),
(5, (SELECT user_id FROM users WHERE username = 'marcus'), 3, 'out', 'Sold to customer');

-- Note: The password toggle functionality is implemented through the js/password-toggle.js file
-- This file should be created during installation or imported from the source code
