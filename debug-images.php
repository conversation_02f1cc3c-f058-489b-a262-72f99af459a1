<?php
// Debug script to check image paths and existence

// Define the product images we need
$images = [
    '13pro.jpg',
    '14pro.jpg',
    's22.jpg',
    'zfold4.jpg',
    'playstation5.jpg'
];

echo "<h1>Image Debug Information</h1>";

// Check if products directory exists
$productsDir = __DIR__ . '/products';
echo "<h2>Products Directory</h2>";
echo "Path: " . $productsDir . "<br>";
echo "Exists: " . (file_exists($productsDir) ? "Yes" : "No") . "<br>";
echo "Is Directory: " . (is_dir($productsDir) ? "Yes" : "No") . "<br>";
echo "Is Readable: " . (is_readable($productsDir) ? "Yes" : "No") . "<br>";

// List all files in the products directory
echo "<h2>Files in Products Directory</h2>";
if (file_exists($productsDir) && is_dir($productsDir)) {
    $files = scandir($productsDir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != "." && $file != "..") {
            echo "<li>" . $file . " (Size: " . filesize($productsDir . '/' . $file) . " bytes)</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p>Cannot list files - directory does not exist or is not readable</p>";
}

// Check each required image
echo "<h2>Required Images Status</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Image Name</th><th>Full Path</th><th>Exists</th><th>Size</th><th>Preview</th></tr>";

foreach ($images as $image) {
    $imagePath = $productsDir . '/' . $image;
    $exists = file_exists($imagePath);
    $size = $exists ? filesize($imagePath) : "N/A";
    
    echo "<tr>";
    echo "<td>" . $image . "</td>";
    echo "<td>" . $imagePath . "</td>";
    echo "<td>" . ($exists ? "Yes" : "No") . "</td>";
    echo "<td>" . $size . "</td>";
    echo "<td>";
    if ($exists) {
        echo "<img src='products/" . $image . "' style='max-width: 100px; max-height: 100px;'>";
    } else {
        echo "No image";
    }
    echo "</td>";
    echo "</tr>";
}

echo "</table>";

// Create images if they don't exist
echo "<h2>Create Missing Images</h2>";
echo "<form method='post'>";
echo "<input type='submit' name='create_images' value='Create Missing Images'>";
echo "</form>";

if (isset($_POST['create_images'])) {
    // Create the products directory if it doesn't exist
    if (!file_exists($productsDir)) {
        mkdir($productsDir, 0777, true);
        echo "<p>Created products directory</p>";
    }
    
    // Create each image
    foreach ($images as $image) {
        $imagePath = $productsDir . '/' . $image;
        
        // Create a simple colored image
        $width = 300;
        $height = 300;
        $im = imagecreatetruecolor($width, $height);
        
        // Set a different color for each product type
        switch ($image) {
            case '13pro.jpg':
            case '14pro.jpg':
                // Blue for iPhones
                $bgColor = imagecolorallocate($im, 0, 122, 255);
                break;
            case 's22.jpg':
            case 'zfold4.jpg':
                // Green for Samsung
                $bgColor = imagecolorallocate($im, 76, 175, 80);
                break;
            case 'playstation5.jpg':
                // Black for PlayStation
                $bgColor = imagecolorallocate($im, 33, 33, 33);
                break;
            default:
                // Gray for others
                $bgColor = imagecolorallocate($im, 158, 158, 158);
        }
        
        // Fill the image with the background color
        imagefill($im, 0, 0, $bgColor);
        
        // Add product name text
        $textColor = imagecolorallocate($im, 255, 255, 255);
        $productName = str_replace('.jpg', '', $image);
        
        // Format the product name for display
        switch ($productName) {
            case '13pro':
                $displayName = 'iPhone 13 Pro';
                break;
            case '14pro':
                $displayName = 'iPhone 14 Pro';
                break;
            case 's22':
                $displayName = 'Samsung S22';
                break;
            case 'zfold4':
                $displayName = 'Z Fold 4';
                break;
            case 'playstation5':
                $displayName = 'PlayStation 5';
                break;
            default:
                $displayName = $productName;
        }
        
        // Center the text
        $fontSize = 5;
        $textWidth = imagefontwidth($fontSize) * strlen($displayName);
        $textX = ($width - $textWidth) / 2;
        $textY = $height / 2 - imagefontheight($fontSize) / 2;
        
        imagestring($im, $fontSize, $textX, $textY, $displayName, $textColor);
        
        // Save the image
        imagejpeg($im, $imagePath, 90);
        imagedestroy($im);
        
        echo "<p>Created image: {$image}</p>";
    }
    
    echo "<p>All images created. <a href='debug-images.php'>Refresh</a> to see the results.</p>";
}

// Links to test pages
echo "<h2>Test Links</h2>";
echo "<ul>";
echo "<li><a href='storekeeper/available-products.php' target='_blank'>Available Products Page</a></li>";
echo "<li><a href='products/13pro.jpg' target='_blank'>Direct link to iPhone 13 Pro image</a></li>";
echo "<li><a href='products/14pro.jpg' target='_blank'>Direct link to iPhone 14 Pro image</a></li>";
echo "<li><a href='products/s22.jpg' target='_blank'>Direct link to Samsung S22 image</a></li>";
echo "<li><a href='products/zfold4.jpg' target='_blank'>Direct link to Z Fold 4 image</a></li>";
echo "<li><a href='products/playstation5.jpg' target='_blank'>Direct link to PlayStation 5 image</a></li>";
echo "</ul>";
?>
