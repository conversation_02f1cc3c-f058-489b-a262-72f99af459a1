<?php
// Direct database reset script
// This script directly executes SQL commands to reset admin accounts

// Database connection parameters
$host = 'localhost';
$dbname = 'griffin_inventory';
$username = 'root';
$password = '';

try {
    // Connect to database
    $conn = new mysqli($host, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    // Step 1: Find a storekeeper to reassign transactions to
    $result = $conn->query("SELECT user_id FROM users WHERE role = 'storekeeper' LIMIT 1");
    $storekeeper = $result->fetch_assoc();
    
    if ($storekeeper) {
        $storekeeper_id = $storekeeper['user_id'];
    } else {
        // Create a temporary storekeeper
        $conn->query("INSERT INTO users (username, password, full_name, email, role, status) 
                     VALUES ('temp_user', '$2y$10$8WxhXQQBZuEYl0H0bxpEbOUUQHX5KGXEQdmnqvfK7NuQm2JC8Lfwi', 
                             'Temporary User', '<EMAIL>', 'storekeeper', 'active')");
        $storekeeper_id = $conn->insert_id;
    }
    
    // Step 2: Get all admin user IDs
    $result = $conn->query("SELECT user_id FROM users WHERE role = 'admin'");
    $admin_ids = [];
    while ($row = $result->fetch_assoc()) {
        $admin_ids[] = $row['user_id'];
    }
    
    if (!empty($admin_ids)) {
        // Step 3: Update all transactions from admin users to reference the storekeeper
        $admin_ids_str = implode(',', $admin_ids);
        $conn->query("UPDATE stock_transactions SET user_id = $storekeeper_id WHERE user_id IN ($admin_ids_str)");
        
        // Step 4: Delete all admin users
        $conn->query("DELETE FROM users WHERE role = 'admin'");
    }
    
    // Commit transaction
    $conn->commit();
    
    $success = true;
    $message = "All admin accounts have been removed successfully. You can now create a new admin account.";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn) && $conn->connect_error === false) {
        $conn->rollback();
    }
    
    $success = false;
    $message = "Error: " . $e->getMessage();
}

// Close connection
if (isset($conn)) {
    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Reset - Griffin Gadgets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f0f5fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 50px 0;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .card-header {
            background-color: #2c3e50;
            color: white;
            font-weight: 600;
            padding: 1.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h4>Griffin Gadgets Inventory System</h4>
                    </div>
                    <div class="card-body p-4 text-center">
                        <?php if ($success): ?>
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h2 class="mb-3">Admin Reset Complete</h2>
                            <p class="lead mb-4"><?php echo $message; ?></p>
                            <div class="d-grid gap-2 col-md-8 mx-auto">
                                <a href="adminsetup.php" class="btn btn-success btn-lg">Create New Admin Account</a>
                                <a href="index.php" class="btn btn-outline-secondary">Return to Home</a>
                            </div>
                        <?php else: ?>
                            <div class="mb-4">
                                <i class="fas fa-exclamation-circle text-danger" style="font-size: 4rem;"></i>
                            </div>
                            <h2 class="mb-3">Error</h2>
                            <p class="lead mb-4"><?php echo $message; ?></p>
                            <div class="d-grid gap-2 col-md-8 mx-auto">
                                <a href="index.php" class="btn btn-primary btn-lg">Return to Home</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
