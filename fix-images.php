<?php
// This script fixes product images by copying sample images to the products directory

// Ensure the products directory exists
$productsDir = __DIR__ . '/products';
if (!file_exists($productsDir)) {
    mkdir($productsDir, 0777, true);
    echo "Created products directory<br>";
}

// Define image mappings
$images = [
    '13pro.jpg',
    '14pro.jpg',
    's22.jpg',
    'zfold4.jpg',
    'playstation5.jpg'
];

// Create a simple colored image for each product
foreach ($images as $imageName) {
    $imagePath = $productsDir . '/' . $imageName;
    
    // Create a blank image
    $width = 300;
    $height = 300;
    $image = imagecreatetruecolor($width, $height);
    
    // Set a different color for each product type
    switch ($imageName) {
        case '13pro.jpg':
        case '14pro.jpg':
            // Blue for iPhones
            $bgColor = imagecolorallocate($image, 0, 122, 255);
            break;
        case 's22.jpg':
        case 'zfold4.jpg':
            // Green for Samsung
            $bgColor = imagecolorallocate($image, 76, 175, 80);
            break;
        case 'playstation5.jpg':
            // Black for PlayStation
            $bgColor = imagecolorallocate($image, 33, 33, 33);
            break;
        default:
            // Gray for others
            $bgColor = imagecolorallocate($image, 158, 158, 158);
    }
    
    // Fill the image with the background color
    imagefill($image, 0, 0, $bgColor);
    
    // Add product name text
    $textColor = imagecolorallocate($image, 255, 255, 255);
    $productName = str_replace('.jpg', '', $imageName);
    
    // Format the product name for display
    switch ($productName) {
        case '13pro':
            $displayName = 'iPhone 13 Pro';
            break;
        case '14pro':
            $displayName = 'iPhone 14 Pro';
            break;
        case 's22':
            $displayName = 'Samsung S22';
            break;
        case 'zfold4':
            $displayName = 'Z Fold 4';
            break;
        case 'playstation5':
            $displayName = 'PlayStation 5';
            break;
        default:
            $displayName = $productName;
    }
    
    // Center the text
    $fontSize = 5;
    $textWidth = imagefontwidth($fontSize) * strlen($displayName);
    $textX = ($width - $textWidth) / 2;
    $textY = $height / 2 - imagefontheight($fontSize) / 2;
    
    imagestring($image, $fontSize, $textX, $textY, $displayName, $textColor);
    
    // Save the image
    imagejpeg($image, $imagePath, 90);
    imagedestroy($image);
    
    echo "Created image: {$imageName}<br>";
}

echo "<p>All product images have been created successfully!</p>";
echo "<p><a href='storekeeper/available-products.php'>Go to Available Products</a></p>";
?>
