<?php
// This script generates placeholder images for testing

// List of required images
$images = [
    '13pro.jpg' => 'iPhone 13 Pro',
    '14pro.jpg' => 'iPhone 14 Pro',
    's22.jpg' => 'Samsung Galaxy S22',
    'zfold4.jpg' => 'Samsung Galaxy Z Fold 4',
    'playstation5.jpg' => 'PlayStation 5'
];

// Create products directory if it doesn't exist
if (!file_exists('products')) {
    mkdir('products', 0777, true);
}

// Generate placeholder images
foreach ($images as $filename => $label) {
    // Create a blank image
    $width = 300;
    $height = 300;
    $image = imagecreatetruecolor($width, $height);
    
    // Colors
    $bg_color = imagecolorallocate($image, 240, 240, 240);
    $text_color = imagecolorallocate($image, 50, 50, 50);
    $border_color = imagecolorallocate($image, 200, 200, 200);
    
    // Fill background
    imagefill($image, 0, 0, $bg_color);
    
    // Draw border
    imagerectangle($image, 0, 0, $width-1, $height-1, $border_color);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($label);
    $text_height = imagefontheight($font_size);
    
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $label, $text_color);
    
    // Save image
    imagejpeg($image, 'products/' . $filename, 90);
    
    // Free memory
    imagedestroy($image);
    
    echo "Generated: products/$filename<br>";
}

echo "<p>All placeholder images have been generated.</p>";
echo "<p><a href='storekeeper/available-products.php'>Go to Available Products</a></p>";
?>
