<?php
// Image upload test script
// This script helps diagnose issues with image uploads

// Include header
include 'includes/header.php';
?>

<div class="container py-5">
    <h1 class="mb-4">Image Upload Test</h1>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Upload Test Form</h5>
                </div>
                <div class="card-body">
                    <form action="" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="test_image" class="form-label">Select Image to Upload</label>
                            <input type="file" name="test_image" id="test_image" class="form-control" accept="image/*" required>
                            <div class="form-text">Select any image file to test the upload functionality.</div>
                        </div>
                        
                        <div class="mb-3">
                            <div id="imagePreview" class="mt-2 text-center d-none">
                                <img src="" alt="Image Preview" class="img-thumbnail" style="max-height: 200px;">
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="submit" class="btn btn-primary">Test Upload</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">System Information</h5>
                </div>
                <div class="card-body">
                    <h6>Server Information:</h6>
                    <ul class="list-group mb-3">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            PHP Version
                            <span class="badge bg-primary rounded-pill"><?php echo phpversion(); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Max Upload Size
                            <span class="badge bg-primary rounded-pill"><?php echo ini_get('upload_max_filesize'); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Max Post Size
                            <span class="badge bg-primary rounded-pill"><?php echo ini_get('post_max_size'); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Memory Limit
                            <span class="badge bg-primary rounded-pill"><?php echo ini_get('memory_limit'); ?></span>
                        </li>
                    </ul>
                    
                    <h6>Directory Permissions:</h6>
                    <?php
                    $upload_dir = "images/products/";
                    $full_path = __DIR__ . '/' . $upload_dir;
                    
                    // Check if directory exists
                    $dir_exists = file_exists($full_path);
                    
                    // Create directory if it doesn't exist
                    if (!$dir_exists) {
                        $dir_created = mkdir($full_path, 0777, true);
                    } else {
                        $dir_created = true;
                    }
                    
                    // Check if directory is writable
                    $is_writable = is_writable($full_path);
                    
                    // Get directory permissions
                    $perms = file_exists($full_path) ? substr(sprintf('%o', fileperms($full_path)), -4) : 'N/A';
                    ?>
                    
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Upload Directory
                            <span><?php echo $full_path; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Directory Exists
                            <span class="badge <?php echo $dir_exists ? 'bg-success' : 'bg-danger'; ?> rounded-pill">
                                <?php echo $dir_exists ? 'Yes' : 'No'; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Directory Created
                            <span class="badge <?php echo $dir_created ? 'bg-success' : 'bg-danger'; ?> rounded-pill">
                                <?php echo $dir_created ? 'Yes' : 'No'; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Directory Writable
                            <span class="badge <?php echo $is_writable ? 'bg-success' : 'bg-danger'; ?> rounded-pill">
                                <?php echo $is_writable ? 'Yes' : 'No'; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Directory Permissions
                            <span class="badge bg-info rounded-pill"><?php echo $perms; ?></span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <?php
    // Process upload test
    if (isset($_POST['submit']) && isset($_FILES['test_image'])) {
        echo '<div class="card shadow-sm mt-4">';
        echo '<div class="card-header bg-primary text-white">';
        echo '<h5 class="mb-0">Upload Test Results</h5>';
        echo '</div>';
        echo '<div class="card-body">';
        
        // Display file information
        echo '<h6>File Information:</h6>';
        echo '<ul class="list-group mb-3">';
        echo '<li class="list-group-item d-flex justify-content-between align-items-center">File Name<span>' . htmlspecialchars($_FILES['test_image']['name']) . '</span></li>';
        echo '<li class="list-group-item d-flex justify-content-between align-items-center">File Type<span>' . htmlspecialchars($_FILES['test_image']['type']) . '</span></li>';
        echo '<li class="list-group-item d-flex justify-content-between align-items-center">File Size<span>' . number_format($_FILES['test_image']['size'] / 1024, 2) . ' KB</span></li>';
        echo '<li class="list-group-item d-flex justify-content-between align-items-center">Error Code<span>' . $_FILES['test_image']['error'] . '</span></li>';
        echo '</ul>';
        
        // Process upload
        if ($_FILES['test_image']['error'] == 0) {
            $allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
            $max_size = 5 * 1024 * 1024; // 5MB
            
            if (!in_array($_FILES['test_image']['type'], $allowed_types)) {
                echo '<div class="alert alert-danger">Error: Only JPG, JPEG, PNG, and GIF files are allowed.</div>';
            } elseif ($_FILES['test_image']['size'] > $max_size) {
                echo '<div class="alert alert-danger">Error: Image size should be less than 5MB.</div>';
            } else {
                // Create directory if it doesn't exist
                if (!file_exists($full_path)) {
                    mkdir($full_path, 0777, true);
                }
                
                // Generate unique filename
                $original_filename = pathinfo($_FILES['test_image']['name'], PATHINFO_FILENAME);
                $file_extension = pathinfo($_FILES['test_image']['name'], PATHINFO_EXTENSION);
                
                // Sanitize filename
                $original_filename = preg_replace("/[^a-zA-Z0-9]/", "_", $original_filename);
                $file_name = $original_filename . '_' . uniqid() . '.' . $file_extension;
                $target_file = $full_path . $file_name;
                $relative_path = $upload_dir . $file_name;
                
                // Upload file
                $upload_success = move_uploaded_file($_FILES['test_image']['tmp_name'], $target_file);
                
                if ($upload_success) {
                    // Set proper permissions
                    chmod($target_file, 0644);
                    
                    echo '<div class="alert alert-success">Image uploaded successfully!</div>';
                    echo '<h6>Upload Details:</h6>';
                    echo '<ul class="list-group mb-3">';
                    echo '<li class="list-group-item d-flex justify-content-between align-items-center">Target File<span>' . $target_file . '</span></li>';
                    echo '<li class="list-group-item d-flex justify-content-between align-items-center">Relative Path<span>' . $relative_path . '</span></li>';
                    echo '<li class="list-group-item d-flex justify-content-between align-items-center">File Exists<span>' . (file_exists($target_file) ? 'Yes' : 'No') . '</span></li>';
                    echo '<li class="list-group-item d-flex justify-content-between align-items-center">File Permissions<span>' . substr(sprintf('%o', fileperms($target_file)), -4) . '</span></li>';
                    echo '</ul>';
                    
                    echo '<div class="text-center mt-3">';
                    echo '<h6>Uploaded Image:</h6>';
                    echo '<img src="' . $relative_path . '" alt="Uploaded Image" class="img-thumbnail" style="max-height: 300px;">';
                    echo '</div>';
                    
                    // Test with getImageUrl function
                    if (function_exists('getImageUrl')) {
                        echo '<h6 class="mt-3">Image URL Test:</h6>';
                        echo '<ul class="list-group">';
                        echo '<li class="list-group-item d-flex justify-content-between align-items-center">getImageUrl Result<span>' . getImageUrl($relative_path) . '</span></li>';
                        echo '</ul>';
                        
                        echo '<div class="text-center mt-3">';
                        echo '<h6>Image Using getImageUrl:</h6>';
                        echo '<img src="' . getImageUrl($relative_path) . '" alt="Uploaded Image" class="img-thumbnail" style="max-height: 300px;">';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="alert alert-danger">Error: Failed to upload image. Error code: ' . $_FILES['test_image']['error'] . '</div>';
                }
            }
        } else {
            echo '<div class="alert alert-danger">Error: ' . getUploadErrorMessage($_FILES['test_image']['error']) . '</div>';
        }
        
        echo '</div>';
        echo '</div>';
    }
    
    // Function to get upload error message
    function getUploadErrorMessage($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return "The uploaded file exceeds the upload_max_filesize directive in php.ini";
            case UPLOAD_ERR_FORM_SIZE:
                return "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form";
            case UPLOAD_ERR_PARTIAL:
                return "The uploaded file was only partially uploaded";
            case UPLOAD_ERR_NO_FILE:
                return "No file was uploaded";
            case UPLOAD_ERR_NO_TMP_DIR:
                return "Missing a temporary folder";
            case UPLOAD_ERR_CANT_WRITE:
                return "Failed to write file to disk";
            case UPLOAD_ERR_EXTENSION:
                return "A PHP extension stopped the file upload";
            default:
                return "Unknown upload error";
        }
    }
    ?>
    
    <div class="mt-4">
        <a href="admin/products.php" class="btn btn-primary">Go to Products Management</a>
        <a href="index.php" class="btn btn-secondary">Go to Home Page</a>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview
    const testImage = document.getElementById('test_image');
    const imagePreview = document.getElementById('imagePreview');

    testImage.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                imagePreview.classList.remove('d-none');
                imagePreview.querySelector('img').src = e.target.result;
            }

            reader.readAsDataURL(this.files[0]);
        } else {
            imagePreview.classList.add('d-none');
        }
    });
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
