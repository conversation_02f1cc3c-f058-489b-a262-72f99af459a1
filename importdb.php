<?php
// Database import script for Griffin Gadgets Inventory System

// Set a longer timeout for large SQL imports
set_time_limit(300); // 5 minutes

// Define database configuration
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'griffin_inventory',
    'sql_file' => 'database/griffin_inventory.sql'
];

// Initialize variables
$success = false;
$error = false;
$message = '';
$log = [];

// Function to log messages
function logMessage($msg, $type = 'info') {
    global $log;
    $log[] = ['type' => $type, 'message' => $msg];
}

// Process import request
if (isset($_POST['import'])) {
    try {
        // Make sure database directory exists
        $db_dir = dirname($db_config['sql_file']);
        if (!is_dir($db_dir)) {
            if (mkdir($db_dir, 0755, true)) {
                logMessage("Created database directory");
            } else {
                logMessage("Failed to create database directory", "error");
                throw new Exception("Failed to create database directory: $db_dir");
            }
        }

        // Check if SQL file exists
        if (!file_exists($db_config['sql_file'])) {
            // Create a basic SQL file if it doesn't exist
            $basic_sql = file_get_contents('database/griffin_inventory.sql');

            if (!$basic_sql) {
                // If we can't read the file, create a new one
                $basic_sql = <<<'EOT'
-- Griffin Gadgets Inventory System Database Schema
-- This file contains the complete database structure for the inventory system

-- Drop database if it exists and create a new one
DROP DATABASE IF EXISTS griffin_inventory;
CREATE DATABASE griffin_inventory;
USE griffin_inventory;

-- Create users table
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    role ENUM('admin', 'storekeeper') NOT NULL,
    status ENUM('active', 'inactive', 'pending') NOT NULL DEFAULT 'pending',
    password_reset_requested BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Create categories table
CREATE TABLE categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE products (
    product_id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    description TEXT,
    quantity INT NOT NULL DEFAULT 0,
    price DECIMAL(12, 2) NOT NULL,
    alert_level INT NOT NULL DEFAULT 5,
    image_path VARCHAR(255) DEFAULT NULL,
    category VARCHAR(50) DEFAULT 'General',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create stock_transactions table
CREATE TABLE stock_transactions (
    transaction_id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    quantity INT NOT NULL,
    transaction_type ENUM('in', 'out') NOT NULL,
    notes TEXT,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Create sales table for recording sales transactions
CREATE TABLE sales (
    sale_id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    quantity INT NOT NULL,
    sale_price DECIMAL(12, 2) NOT NULL,
    customer_name VARCHAR(100),
    customer_phone VARCHAR(20),
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Create system_logs table for tracking system activities
CREATE TABLE system_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action_type VARCHAR(50) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    log_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Admin user will be created through the adminsetup.php page

-- Insert default categories
INSERT INTO categories (category_name) VALUES
('Phones'),
('Laptops'),
('Accessories'),
('Tablets'),
('Wearables'),
('Gaming');

-- Insert sample products
INSERT INTO products (product_name, description, quantity, price, alert_level, image_path, category) VALUES
('iPhone 13 Pro', 'Apple iPhone 13 Pro 128GB', 15, 550000.00, 5, 'images/products/13pro.jpg', 'Phones'),
('iPhone 14', 'Apple iPhone 14 256GB', 10, 650000.00, 3, 'images/products/14pro.jpg', 'Phones'),
('Samsung Galaxy S22', 'Samsung Galaxy S22 Ultra 256GB', 8, 480000.00, 3, 'images/products/s22.jpg', 'Phones'),
('Samsung Galaxy Z Fold 4', 'Samsung Galaxy Z Fold 4 512GB', 5, 750000.00, 2, 'images/products/zfold4.jpg', 'Phones'),
('PlayStation 5', 'Sony PlayStation 5 Digital Edition', 7, 350000.00, 3, 'images/products/playstation5.jpg', 'Gaming');

-- Insert sample storekeeper (username: storekeeper, password: store123)
INSERT INTO users (username, password, full_name, email, role, status)
VALUES ('marcus', '$2y$10$8WxhXQQBZuEYl0H0bxpEbOUUQHX5KGXEQdmnqvfK7NuQm2JC8Lfwi', 'MARCUS', '<EMAIL>', 'storekeeper', 'active');

-- Insert sample admin user (username: admin, password: admin123) for initial transactions
-- This will be replaced by the actual admin setup process
INSERT INTO users (username, password, full_name, email, role, status)
VALUES ('temp_admin', '$2y$10$8WxhXQQBZuEYl0H0bxpEbOUUQHX5KGXEQdmnqvfK7NuQm2JC8Lfwi', 'Temporary Admin', '<EMAIL>', 'admin', 'active');

-- Insert sample transactions
-- Using subqueries to get the actual user_id values
INSERT INTO stock_transactions (product_id, user_id, quantity, transaction_type, notes) VALUES
(1, (SELECT user_id FROM users WHERE username = 'temp_admin'), 20, 'in', 'Initial stock'),
(2, (SELECT user_id FROM users WHERE username = 'temp_admin'), 15, 'in', 'Initial stock'),
(3, (SELECT user_id FROM users WHERE username = 'temp_admin'), 10, 'in', 'Initial stock'),
(4, (SELECT user_id FROM users WHERE username = 'temp_admin'), 8, 'in', 'Initial stock'),
(5, (SELECT user_id FROM users WHERE username = 'temp_admin'), 10, 'in', 'Initial stock'),
(1, (SELECT user_id FROM users WHERE username = 'marcus'), 5, 'out', 'Sold to customer'),
(2, (SELECT user_id FROM users WHERE username = 'marcus'), 3, 'out', 'Sold to customer'),
(3, (SELECT user_id FROM users WHERE username = 'marcus'), 2, 'out', 'Sold to customer'),
(4, (SELECT user_id FROM users WHERE username = 'marcus'), 3, 'out', 'Sold to customer'),
(5, (SELECT user_id FROM users WHERE username = 'marcus'), 3, 'out', 'Sold to customer');

-- Note: The password toggle functionality is implemented through the js/password-toggle.js file
-- This file should be created during installation or imported from the source code
EOT;

                if (file_put_contents($db_config['sql_file'], $basic_sql)) {
                    logMessage("Created SQL file: {$db_config['sql_file']}");
                } else {
                    throw new Exception("Failed to create SQL file: {$db_config['sql_file']}");
                }
            }
        }

        logMessage("SQL file found: {$db_config['sql_file']}");

        // Connect to MySQL server (without specifying database)
        $mysqli = new mysqli($db_config['host'], $db_config['username'], $db_config['password']);

        if ($mysqli->connect_error) {
            throw new Exception("Connection failed: " . $mysqli->connect_error);
        }

        // Check if MySQL server is running properly
        try {
            $mysqli->query("SELECT 1");
        } catch (Exception $e) {
            throw new Exception("MySQL server is not responding properly. Please check if MySQL service is running.");
        }

        logMessage("Connected to MySQL server successfully");

        // Read SQL file
        $sql = file_get_contents($db_config['sql_file']);

        if (!$sql) {
            throw new Exception("Failed to read SQL file");
        }

        // Basic validation of SQL file
        if (strlen($sql) < 100) {
            throw new Exception("SQL file appears to be too small or empty. Please check the file content.");
        }

        // Check for basic SQL structure
        if (!preg_match('/CREATE\s+TABLE/i', $sql)) {
            throw new Exception("SQL file does not contain CREATE TABLE statements. Please check the file content.");
        }

        logMessage("SQL file read successfully");

        // Check if js directory exists, if not create it
        if (!is_dir('js')) {
            if (mkdir('js', 0755)) {
                logMessage("Created js directory");
            } else {
                logMessage("Failed to create js directory", "error");
            }
        }

        // Check if images directory exists, if not create it
        if (!is_dir('images')) {
            if (mkdir('images', 0755)) {
                logMessage("Created images directory");
            } else {
                logMessage("Failed to create images directory", "error");
            }
        }

        // Check if images/products directory exists, if not create it
        if (!is_dir('images/products')) {
            if (mkdir('images/products', 0755, true)) {
                logMessage("Created images/products directory");
            } else {
                logMessage("Failed to create images/products directory", "error");
            }
        }

        // Check if password-toggle.js exists, if not create it
        if (!file_exists('js/password-toggle.js')) {
            $password_toggle_js = <<<'EOT'
/**
 * Password Toggle Visibility
 * This script adds eye icons to password fields to toggle visibility
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find all password input fields
    const passwordFields = document.querySelectorAll('input[type="password"]');

    // Add toggle button to each password field
    passwordFields.forEach(function(field) {
        // Create the toggle button
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.className = 'btn btn-outline-secondary password-toggle';
        toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        toggleButton.title = 'Show password';
        toggleButton.setAttribute('aria-label', 'Toggle password visibility');

        // Create input group if not already in one
        let inputGroup = field.parentElement;
        if (!inputGroup.classList.contains('input-group')) {
            // If the parent is not an input-group, check if it's a form-floating
            if (inputGroup.classList.contains('form-floating')) {
                // For form-floating, we need to wrap the input in an input-group
                // and keep the label outside
                const label = inputGroup.querySelector('label');
                const newInputGroup = document.createElement('div');
                newInputGroup.className = 'input-group';

                // Replace the input with the input-group
                field.parentNode.insertBefore(newInputGroup, field);
                newInputGroup.appendChild(field);

                inputGroup = newInputGroup;
            } else {
                // For regular inputs, just wrap in an input-group
                const newInputGroup = document.createElement('div');
                newInputGroup.className = 'input-group';

                // Replace the input with the input-group
                field.parentNode.insertBefore(newInputGroup, field);
                newInputGroup.appendChild(field);

                inputGroup = newInputGroup;
            }
        }

        // Add the toggle button to the input group
        inputGroup.appendChild(toggleButton);

        // Add click event to toggle password visibility
        toggleButton.addEventListener('click', function() {
            // Toggle the password field type
            if (field.type === 'password') {
                field.type = 'text';
                toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
                toggleButton.title = 'Hide password';
            } else {
                field.type = 'password';
                toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
                toggleButton.title = 'Show password';
            }
        });
    });

    // Add some CSS for the toggle button
    const style = document.createElement('style');
    style.textContent = `
        .password-toggle {
            cursor: pointer;
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }

        .form-floating .input-group {
            height: 100%;
        }

        .form-floating > .input-group > .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
    `;
    document.head.appendChild(style);
});
EOT;

            if (file_put_contents('js/password-toggle.js', $password_toggle_js)) {
                logMessage("Created password-toggle.js file");
            } else {
                logMessage("Failed to create password-toggle.js file", "error");
            }
        }

        // Create admin/reset_system.php if it doesn't exist
        if (!file_exists('admin/reset_system.php')) {
            // Make sure admin directory exists
            if (!is_dir('admin')) {
                if (mkdir('admin', 0755, true)) {
                    logMessage("Created admin directory");
                } else {
                    logMessage("Failed to create admin directory", "error");
                }
            }

            $admin_reset_system = <<<'EOT'
<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize response variables
$error = '';
$success = '';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify that the confirm checkbox was checked
    if (!isset($_POST['confirm_reset']) || $_POST['confirm_reset'] !== 'on') {
        $error = "You must confirm that you understand the consequences of this action.";
    }
    // Verify that password was provided
    elseif (!isset($_POST['admin_password']) || empty($_POST['admin_password'])) {
        $error = "Admin password is required to perform this action.";
    }
    else {
        // Get the admin's password from the database
        $admin_id = $_SESSION['user_id'];
        $stmt = $pdo->prepare("SELECT password FROM users WHERE user_id = ? AND role = 'admin'");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch();

        // Verify the password
        if (!$admin || !password_verify($_POST['admin_password'], $admin['password'])) {
            $error = "Invalid password. System reset aborted.";
        } else {
            // Password is correct, proceed with system reset
            try {
                // Start transaction
                $pdo->beginTransaction();

                // First, check if tables exist before attempting to delete from them
                // We'll do this outside of a transaction to avoid issues

                // Check for stock_transactions table
                $hasStockTransactions = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'stock_transactions'");
                $hasStockTransactions = ($stmt && $stmt->rowCount() > 0);

                // Check for products table
                $hasProducts = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'products'");
                $hasProducts = ($stmt && $stmt->rowCount() > 0);

                // Check for sales table
                $hasSales = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'sales'");
                $hasSales = ($stmt && $stmt->rowCount() > 0);

                // Check for categories table
                $hasCategories = false;
                $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
                $hasCategories = ($stmt && $stmt->rowCount() > 0);

                // Temporarily disable foreign key checks
                try {
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
                } catch (Exception $e) {
                    $error .= "Error disabling foreign key checks: " . $e->getMessage() . ". ";
                }

                // 1. Delete stock transactions
                if ($hasStockTransactions) {
                    try {
                        $pdo->exec("TRUNCATE TABLE stock_transactions");
                    } catch (Exception $e) {
                        $error .= "Error clearing stock transactions: " . $e->getMessage() . ". ";
                    }
                }

                // 2. Delete products
                if ($hasProducts) {
                    try {
                        $pdo->exec("TRUNCATE TABLE products");
                    } catch (Exception $e) {
                        $error .= "Error clearing products: " . $e->getMessage() . ". ";
                    }
                }

                // 3. Delete sales records
                if ($hasSales) {
                    try {
                        $pdo->exec("TRUNCATE TABLE sales");
                    } catch (Exception $e) {
                        $error .= "Error clearing sales: " . $e->getMessage() . ". ";
                    }
                }

                // Re-enable foreign key checks
                try {
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
                } catch (Exception $e) {
                    $error .= "Error re-enabling foreign key checks: " . $e->getMessage() . ". ";
                }

                // 4. Ensure default categories exist
                if ($hasCategories) {
                    try {
                        // Don't delete categories, just make sure default ones exist
                        $categories = ['Phones', 'Laptops', 'Accessories', 'Tablets', 'Wearables', 'Gaming'];
                        foreach ($categories as $category) {
                            $check = $pdo->prepare("SELECT * FROM categories WHERE category_name = ?");
                            $check->execute([$category]);
                            if ($check->rowCount() == 0) {
                                // Category doesn't exist, add it
                                $insert = $pdo->prepare("INSERT INTO categories (category_name) VALUES (?)");
                                $insert->execute([$category]);
                            }
                        }
                    } catch (Exception $e) {
                        $error .= "Error managing categories: " . $e->getMessage() . ". ";
                    }
                }

                // Commit transaction
                $pdo->commit();

                // If we have no errors, set success message
                if (empty($error)) {
                    $success = "System has been successfully reset. All products and sales data have been cleared.";

                    // Log the reset action
                    try {
                        logAction($pdo, 'system_reset', 'Admin reset the system - cleared all products and sales data', $_SESSION['user_id']);
                    } catch (Exception $e) {
                        // Just log to error_log if logging fails, don't show to user
                        error_log("Failed to log reset action: " . $e->getMessage());
                    }
                }

            } catch (Exception $e) {
                // Rollback transaction on error
                $pdo->rollBack();
                $error = "An error occurred during system reset: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Reset - Griffin Gadgets</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/all.min.css">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        .reset-result {
            max-width: 600px;
            margin: 100px auto;
            text-align: center;
        }
        .icon-container {
            margin-bottom: 20px;
        }
        .icon-container i {
            font-size: 5rem;
        }
        .success-icon {
            color: #28a745;
        }
        .error-icon {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="reset-result">
            <?php if ($success): ?>
                <div class="icon-container">
                    <i class="fas fa-check-circle success-icon"></i>
                </div>
                <h2>System Reset Successful</h2>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
                <p>The system has been reset to its initial state. All products and sales data have been cleared.</p>
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-primary">Return to Dashboard</a>
                </div>
            <?php elseif ($error): ?>
                <div class="icon-container">
                    <i class="fas fa-times-circle error-icon"></i>
                </div>
                <h2>System Reset Failed</h2>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <p>The system reset operation could not be completed. Please try again.</p>
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-primary">Return to Dashboard</a>
                </div>
            <?php else: ?>
                <div class="icon-container">
                    <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                </div>
                <h2>Unauthorized Access</h2>
                <div class="alert alert-warning">
                    Direct access to this page is not allowed.
                </div>
                <p>Please use the reset button on the dashboard to perform a system reset.</p>
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-primary">Return to Dashboard</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="../js/bootstrap.bundle.min.js"></script>
</body>
</html>
EOT;

            if (file_put_contents('admin/reset_system.php', $admin_reset_system)) {
                logMessage("Created admin/reset_system.php file");
            } else {
                logMessage("Failed to create admin/reset_system.php file", "error");
            }
        }

        // Create admin/stock-book.php if it doesn't exist
        if (!file_exists('admin/stock-book.php')) {
            // Make sure admin directory exists
            if (!is_dir('admin')) {
                if (mkdir('admin', 0755, true)) {
                    logMessage("Created admin directory");
                } else {
                    logMessage("Failed to create admin directory", "error");
                }
            }

            $admin_stock_book = <<<'EOT'
<?php
// Set admin flag
$is_admin = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize variables
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$report_data = [];
$total_opening_value = 0;
$total_closing_value = 0;
$total_received_value = 0;
$total_sold_value = 0;

// Generate report if dates are set
if ($start_date && $end_date) {
    $report_data = getStockBookReport($pdo, $start_date, $end_date);

    // Calculate totals
    foreach ($report_data as $item) {
        $total_opening_value += $item['opening_stock'] * $item['price'];
        $total_closing_value += $item['closing_value'];
        $total_received_value += $item['received'] * $item['price'];
        $total_sold_value += $item['sold'] * $item['price'];
    }
}

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Stock Book Report</h1>
    <button id="printReport" class="btn btn-primary">
        <i class="fas fa-print me-1"></i> Print Report
    </button>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-body p-4">
        <form method="get" action="stock-book.php" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i> Generate Report
                </button>
            </div>
        </form>
    </div>
</div>

<div id="reportContent">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">Stock Book Report</h5>
                    <p class="text-muted mb-0">Period: <?php echo date('d M, Y', strtotime($start_date)); ?> - <?php echo date('d M, Y', strtotime($end_date)); ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5 class="mb-0">Griffin Gadgets</h5>
                    <p class="text-muted mb-0">37a St. Michaels Aba</p>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th class="text-end">Price (₦)</th>
                            <th class="text-end">Opening Stock</th>
                            <th class="text-end">Received</th>
                            <th class="text-end">Sold</th>
                            <th class="text-end">Closing Stock</th>
                            <th class="text-end">Closing Value (₦)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($report_data) > 0): ?>
                            <?php foreach ($report_data as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['category']); ?></td>
                                    <td class="text-end"><?php echo number_format($item['price'], 0); ?></td>
                                    <td class="text-end"><?php echo $item['opening_stock']; ?></td>
                                    <td class="text-end"><?php echo $item['received']; ?></td>
                                    <td class="text-end"><?php echo $item['sold']; ?></td>
                                    <td class="text-end"><?php echo $item['closing_stock']; ?></td>
                                    <td class="text-end"><?php echo number_format($item['closing_value'], 0); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">No data available for the selected period.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th colspan="3">TOTALS</th>
                            <th class="text-end">-</th>
                            <th class="text-end">-</th>
                            <th class="text-end"><?php echo number_format($total_sold_value, 0); ?> ₦</th>
                            <th colspan="2" class="text-end"><?php echo number_format($total_closing_value, 0); ?> ₦</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4 text-center d-none d-print-block">
        <p>Report generated on <?php echo date('d M, Y H:i'); ?></p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Print functionality
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });

    // Validate date range
    document.querySelector('form').addEventListener('submit', function(e) {
        const startDate = new Date(document.getElementById('start_date').value);
        const endDate = new Date(document.getElementById('end_date').value);

        if (startDate > endDate) {
            e.preventDefault();
            alert('Start date cannot be after end date');
        }
    });
});
</script>

<style>
@media print {
    body * {
        visibility: hidden;
    }
    #reportContent, #reportContent * {
        visibility: visible;
    }
    #reportContent {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table {
        width: 100%;
    }
}
</style>

<?php
// Include footer
include '../includes/footer.php';
?>
EOT;

            if (file_put_contents('admin/stock-book.php', $admin_stock_book)) {
                logMessage("Created admin/stock-book.php file");
            } else {
                logMessage("Failed to create admin/stock-book.php file", "error");
            }
        }

        // Make sure includes directory exists
        if (!is_dir('includes')) {
            if (mkdir('includes', 0755, true)) {
                logMessage("Created includes directory");
            } else {
                logMessage("Failed to create includes directory", "error");
            }
        }

        // Create includes/db_connect.php if it doesn't exist
        if (!file_exists('includes/db_connect.php')) {
            $db_connect = <<<'EOT'
<?php
// Database connection parameters
$host = 'localhost';
$dbname = 'griffin_inventory';
$username = 'root';
$password = '';
$charset = 'utf8mb4';

// DSN (Data Source Name)
$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

// PDO options
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

// Try to connect to the database
try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    // If connection fails, try to create the database
    try {
        $pdo = new PDO("mysql:host=$host;charset=$charset", $username, $password, $options);
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbname`");
    } catch (PDOException $e) {
        // If that also fails, show an error message
        die("Could not connect to the database. Error: " . $e->getMessage());
    }
}
?>
EOT;

            if (file_put_contents('includes/db_connect.php', $db_connect)) {
                logMessage("Created includes/db_connect.php file");
            } else {
                logMessage("Failed to create includes/db_connect.php file", "error");
            }
        }

        // Create or update includes/functions.php
        if (file_exists('includes/functions.php')) {
            $functions_content = file_get_contents('includes/functions.php');

            // Check if getStockBookReport function already exists
            if (strpos($functions_content, 'function getStockBookReport') === false) {
                // Add the function before the closing PHP tag
                $stock_book_function = <<<'EOT'

// Function to generate stock book report data
function getStockBookReport($pdo, $start_date, $end_date) {
    // Format dates for SQL query
    $start_date = date('Y-m-d 00:00:00', strtotime($start_date));
    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));

    // Get all products
    $products_stmt = $pdo->query("SELECT * FROM products ORDER BY product_name");
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

    $report_data = [];

    foreach ($products as $product) {
        $product_id = $product['product_id'];

        // Get opening stock (stock at start_date)
        $opening_stmt = $pdo->prepare("
            SELECT
                COALESCE(
                    (SELECT quantity FROM products WHERE product_id = :product_id) -
                    COALESCE(SUM(CASE WHEN transaction_type = 'in' THEN quantity ELSE 0 END), 0) +
                    COALESCE(SUM(CASE WHEN transaction_type = 'out' THEN quantity ELSE 0 END), 0),
                    0
                ) as opening_stock
            FROM stock_transactions
            WHERE product_id = :product_id AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $opening_stmt->bindParam(':product_id', $product_id);
        $opening_stmt->bindParam(':start_date', $start_date);
        $opening_stmt->bindParam(':end_date', $end_date);
        $opening_stmt->execute();
        $opening_stock = $opening_stmt->fetch(PDO::FETCH_ASSOC)['opening_stock'];

        // Get stock received during period
        $received_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as received
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'in'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $received_stmt->bindParam(':product_id', $product_id);
        $received_stmt->bindParam(':start_date', $start_date);
        $received_stmt->bindParam(':end_date', $end_date);
        $received_stmt->execute();
        $received = $received_stmt->fetch(PDO::FETCH_ASSOC)['received'];

        // Get stock sold during period
        $sold_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as sold
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'out'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $sold_stmt->bindParam(':product_id', $product_id);
        $sold_stmt->bindParam(':start_date', $start_date);
        $sold_stmt->bindParam(':end_date', $end_date);
        $sold_stmt->execute();
        $sold = $sold_stmt->fetch(PDO::FETCH_ASSOC)['sold'];

        // Calculate closing stock
        $closing_stock = $opening_stock + $received - $sold;

        // Add to report data
        $report_data[] = [
            'product_id' => $product_id,
            'product_name' => $product['product_name'],
            'category' => $product['category'],
            'price' => $product['price'],
            'opening_stock' => $opening_stock,
            'received' => $received,
            'sold' => $sold,
            'closing_stock' => $closing_stock,
            'closing_value' => $closing_stock * $product['price']
        ];
    }

    return $report_data;
}
EOT;

                // Add the function to the file
                $functions_content = str_replace('?>', $stock_book_function . "\n?>", $functions_content);

                // Save the updated file
                if (file_put_contents('includes/functions.php', $functions_content)) {
                    logMessage("Updated includes/functions.php with getStockBookReport function");
                } else {
                    logMessage("Failed to update includes/functions.php", "error");
                }
            } else {
                logMessage("getStockBookReport function already exists in includes/functions.php");
            }
        } else {
            // Create a basic functions.php file with essential functions
            $basic_functions = <<<'EOT'
<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Function to check if user is storekeeper
function isStorekeeper() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'storekeeper';
}

// Function to redirect if not logged in
function requireLogin() {
    if (!isLoggedIn()) {
        header("Location: ../login.php");
        exit;
    }
}

// Function to redirect if not admin
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header("Location: ../index.php?error=unauthorized");
        exit;
    }
}

// Function to redirect if not storekeeper
function requireStorekeeper() {
    requireLogin();
    if (!isStorekeeper()) {
        header("Location: ../index.php?error=unauthorized");
        exit;
    }
}

// Function to format currency
function formatCurrency($amount) {
    return '₦' . number_format($amount, 0);
}

// Function to generate alert badge based on stock level
function getStockStatusBadge($quantity, $alert_level) {
    if ($quantity <= 0) {
        return '<span class="badge bg-danger">Out of Stock</span>';
    } elseif ($quantity <= $alert_level) {
        return '<span class="badge bg-warning text-dark">Low Stock</span>';
    } else {
        return '<span class="badge bg-success">In Stock</span>';
    }
}

// Function to get product by ID
function getProductById($pdo, $product_id) {
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    return $stmt->fetch();
}

// Function to log stock transaction
function logStockTransaction($pdo, $product_id, $user_id, $transaction_type, $quantity, $notes = '') {
    // Verify user exists in database
    $check_user = $pdo->prepare("SELECT user_id FROM users WHERE user_id = ?");
    $check_user->execute([$user_id]);

    if ($check_user->rowCount() > 0) {
        // User exists, proceed with transaction
        $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, ?, ?, ?)");
        return $stmt->execute([$product_id, $user_id, $transaction_type, $quantity, $notes]);
    } else {
        // User doesn't exist, use a default admin user from the database
        $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
        $admin = $get_admin->fetch();

        if ($admin && isset($admin['user_id'])) {
            $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, ?, ?, ?)");
            return $stmt->execute([$product_id, $admin['user_id'], $transaction_type, $quantity, $notes . ' (system)']);
        } else {
            // No admin found, just update the product quantity without logging
            error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
            return false;
        }
    }
}

// Function to update product quantity
function updateProductQuantity($pdo, $product_id, $new_quantity) {
    $stmt = $pdo->prepare("UPDATE products SET quantity = ? WHERE product_id = ?");
    return $stmt->execute([$new_quantity, $product_id]);
}

// Function to get low stock products
function getLowStockProducts($pdo) {
    $stmt = $pdo->query("
        SELECT * FROM products
        WHERE quantity <= alert_level
        ORDER BY (quantity = 0) DESC, quantity ASC
    ");
    return $stmt->fetchAll();
}

// Function to get recent transactions
function getRecentTransactions($pdo, $limit = 10) {
    $stmt = $pdo->prepare("
        SELECT t.*, p.product_name, u.username, u.full_name
        FROM stock_transactions t
        JOIN products p ON t.product_id = p.product_id
        JOIN users u ON t.user_id = u.user_id
        ORDER BY t.transaction_date DESC
        LIMIT ?
    ");
    $stmt->bindParam(1, $limit, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll();
}

// Function to get transactions by date
function getTransactionsByDate($pdo, $date) {
    $stmt = $pdo->prepare("
        SELECT t.*, p.product_name, u.username, u.full_name
        FROM stock_transactions t
        JOIN products p ON t.product_id = p.product_id
        JOIN users u ON t.user_id = u.user_id
        WHERE DATE(t.transaction_date) = ?
        ORDER BY t.transaction_date DESC
    ");
    $stmt->execute([$date]);
    return $stmt->fetchAll();
}

// Function to get CSS class for stock level display
function getStockLevelClass($quantity, $alert_level) {
    if ($quantity <= 0) {
        return 'text-danger';
    } elseif ($quantity <= $alert_level) {
        return 'text-warning';
    } else {
        return 'text-success';
    }
}

// Function to log system actions
function logAction($pdo, $action_type, $description, $user_id = null) {
    // If no user_id is provided, use the current logged-in user
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    // If still no user_id, try to get an admin user
    if ($user_id === null) {
        $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
        $admin = $get_admin->fetch();

        if ($admin && isset($admin['user_id'])) {
            $user_id = $admin['user_id'];
        } else {
            // No user found, log the action without a user ID
            error_log("Warning: No valid user found for action log: $action_type - $description");
            return false;
        }
    }

    // Create system_logs table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action_type VARCHAR(50) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            log_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
        )
    ");

    // Insert the log entry
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action_type, description, ip_address)
        VALUES (?, ?, ?, ?)
    ");

    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

    return $stmt->execute([$user_id, $action_type, $description, $ip]);
}

// Function to get image URL with proper path handling
function getImageUrl($imagePath) {
    // If the path is empty, return a placeholder
    if (empty($imagePath)) {
        return 'https://via.placeholder.com/300x200?text=No+Image';
    }

    // Check if the path is a URL
    if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
        return $imagePath;
    }

    // Check if the path is relative to the document root
    $docRoot = $_SERVER['DOCUMENT_ROOT'];
    $fullPath = $docRoot . '/' . ltrim($imagePath, '/');

    if (file_exists($fullPath)) {
        // Convert to web path
        $webPath = '/' . ltrim($imagePath, '/');
        return $webPath;
    }

    // Check if the path exists relative to the current script
    $scriptDir = dirname($_SERVER['SCRIPT_FILENAME']);
    $relativePath = $scriptDir . '/' . ltrim($imagePath, '/');

    if (file_exists($relativePath)) {
        // Get the relative path from document root
        $relativeToRoot = substr($relativePath, strlen($docRoot));
        return $relativeToRoot;
    }

    // If all else fails, return the original path
    return $imagePath;
}

// Function to generate stock book report data
function getStockBookReport($pdo, $start_date, $end_date) {
    // Format dates for SQL query
    $start_date = date('Y-m-d 00:00:00', strtotime($start_date));
    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));

    // Get all products
    $products_stmt = $pdo->query("SELECT * FROM products ORDER BY product_name");
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

    $report_data = [];

    foreach ($products as $product) {
        $product_id = $product['product_id'];

        // Get opening stock (stock at start_date)
        $opening_stmt = $pdo->prepare("
            SELECT
                COALESCE(
                    (SELECT quantity FROM products WHERE product_id = :product_id) -
                    COALESCE(SUM(CASE WHEN transaction_type = 'in' THEN quantity ELSE 0 END), 0) +
                    COALESCE(SUM(CASE WHEN transaction_type = 'out' THEN quantity ELSE 0 END), 0),
                    0
                ) as opening_stock
            FROM stock_transactions
            WHERE product_id = :product_id AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $opening_stmt->bindParam(':product_id', $product_id);
        $opening_stmt->bindParam(':start_date', $start_date);
        $opening_stmt->bindParam(':end_date', $end_date);
        $opening_stmt->execute();
        $opening_stock = $opening_stmt->fetch(PDO::FETCH_ASSOC)['opening_stock'];

        // Get stock received during period
        $received_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as received
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'in'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $received_stmt->bindParam(':product_id', $product_id);
        $received_stmt->bindParam(':start_date', $start_date);
        $received_stmt->bindParam(':end_date', $end_date);
        $received_stmt->execute();
        $received = $received_stmt->fetch(PDO::FETCH_ASSOC)['received'];

        // Get stock sold during period
        $sold_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as sold
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'out'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $sold_stmt->bindParam(':product_id', $product_id);
        $sold_stmt->bindParam(':start_date', $start_date);
        $sold_stmt->bindParam(':end_date', $end_date);
        $sold_stmt->execute();
        $sold = $sold_stmt->fetch(PDO::FETCH_ASSOC)['sold'];

        // Calculate closing stock
        $closing_stock = $opening_stock + $received - $sold;

        // Add to report data
        $report_data[] = [
            'product_id' => $product_id,
            'product_name' => $product['product_name'],
            'category' => $product['category'],
            'price' => $product['price'],
            'opening_stock' => $opening_stock,
            'received' => $received,
            'sold' => $sold,
            'closing_stock' => $closing_stock,
            'closing_value' => $closing_stock * $product['price']
        ];
    }

    return $report_data;
}
?>
EOT;

            if (file_put_contents('includes/functions.php', $basic_functions)) {
                logMessage("Created includes/functions.php file with all necessary functions");
            } else {
                logMessage("Failed to create includes/functions.php file", "error");
            }
        }

        // Create storekeeper/stock-book.php if it doesn't exist
        if (!file_exists('storekeeper/stock-book.php')) {
            // Make sure storekeeper directory exists
            if (!is_dir('storekeeper')) {
                if (mkdir('storekeeper', 0755, true)) {
                    logMessage("Created storekeeper directory");
                } else {
                    logMessage("Failed to create storekeeper directory", "error");
                }
            }

            $storekeeper_stock_book = <<<'EOT'
<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize variables
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$report_data = [];
$total_opening_value = 0;
$total_closing_value = 0;
$total_received_value = 0;
$total_sold_value = 0;

// Generate report if dates are set
if ($start_date && $end_date) {
    $report_data = getStockBookReport($pdo, $start_date, $end_date);

    // Calculate totals
    foreach ($report_data as $item) {
        $total_opening_value += $item['opening_stock'] * $item['price'];
        $total_closing_value += $item['closing_value'];
        $total_received_value += $item['received'] * $item['price'];
        $total_sold_value += $item['sold'] * $item['price'];
    }
}

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Stock Book Report</h1>
    <button id="printReport" class="btn btn-primary">
        <i class="fas fa-print me-1"></i> Print Report
    </button>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-body p-4">
        <form method="get" action="stock-book.php" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i> Generate Report
                </button>
            </div>
        </form>
    </div>
</div>

<div id="reportContent">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">Stock Book Report</h5>
                    <p class="text-muted mb-0">Period: <?php echo date('d M, Y', strtotime($start_date)); ?> - <?php echo date('d M, Y', strtotime($end_date)); ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5 class="mb-0">Griffin Gadgets</h5>
                    <p class="text-muted mb-0">37a St. Michaels Aba</p>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th class="text-end">Price (₦)</th>
                            <th class="text-end">Opening Stock</th>
                            <th class="text-end">Received</th>
                            <th class="text-end">Sold</th>
                            <th class="text-end">Closing Stock</th>
                            <th class="text-end">Closing Value (₦)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($report_data) > 0): ?>
                            <?php foreach ($report_data as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['category']); ?></td>
                                    <td class="text-end"><?php echo number_format($item['price'], 0); ?></td>
                                    <td class="text-end"><?php echo $item['opening_stock']; ?></td>
                                    <td class="text-end"><?php echo $item['received']; ?></td>
                                    <td class="text-end"><?php echo $item['sold']; ?></td>
                                    <td class="text-end"><?php echo $item['closing_stock']; ?></td>
                                    <td class="text-end"><?php echo number_format($item['closing_value'], 0); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">No data available for the selected period.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th colspan="3">TOTALS</th>
                            <th class="text-end">-</th>
                            <th class="text-end">-</th>
                            <th class="text-end"><?php echo number_format($total_sold_value, 0); ?> ₦</th>
                            <th colspan="2" class="text-end"><?php echo number_format($total_closing_value, 0); ?> ₦</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4 text-center d-none d-print-block">
        <p>Report generated on <?php echo date('d M, Y H:i'); ?></p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Print functionality
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });

    // Validate date range
    document.querySelector('form').addEventListener('submit', function(e) {
        const startDate = new Date(document.getElementById('start_date').value);
        const endDate = new Date(document.getElementById('end_date').value);

        if (startDate > endDate) {
            e.preventDefault();
            alert('Start date cannot be after end date');
        }
    });
});
</script>

<style>
@media print {
    body * {
        visibility: hidden;
    }
    #reportContent, #reportContent * {
        visibility: visible;
    }
    #reportContent {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table {
        width: 100%;
    }
}
</style>

<?php
// Include footer
include '../includes/footer.php';
?>
EOT;

            if (file_put_contents('storekeeper/stock-book.php', $storekeeper_stock_book)) {
                logMessage("Created storekeeper/stock-book.php file");
            } else {
                logMessage("Failed to create storekeeper/stock-book.php file", "error");
            }
        }

        // Disable foreign key checks before import
        $mysqli->query("SET FOREIGN_KEY_CHECKS = 0");
        logMessage("Disabled foreign key checks for import");

        // Drop database if it exists and create a new one
        $mysqli->query("DROP DATABASE IF EXISTS {$db_config['database']}");
        $mysqli->query("CREATE DATABASE {$db_config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

        // Select the database
        if (!$mysqli->select_db($db_config['database'])) {
            throw new Exception("Failed to select database: {$db_config['database']}. Error: " . $mysqli->error);
        }

        logMessage("Created fresh database: {$db_config['database']}");
        logMessage("Selected database: {$db_config['database']}");

        // SIMPLIFIED APPROACH: Execute SQL file directly using mysqli_multi_query
        // This is more reliable than splitting and executing queries individually

        // First, modify the SQL file to ensure it uses the correct database
        // Remove any existing USE, CREATE DATABASE, or DROP DATABASE statements
        $sql = preg_replace('/DROP\s+DATABASE\s+.*?;/i', '', $sql);
        $sql = preg_replace('/CREATE\s+DATABASE\s+.*?;/i', '', $sql);
        $sql = preg_replace('/USE\s+.*?;/i', '', $sql);

        // Add USE statement at the beginning
        $sql = "USE `{$db_config['database']}`;\n" . $sql;

        // Split the SQL into smaller chunks to avoid timeout issues
        // This is especially important for large SQL files
        $sqlChunks = [];
        $statements = explode(';', $sql);
        $currentChunk = '';

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement)) continue;

            // Add statement to current chunk
            $currentChunk .= $statement . ';';

            // If chunk is getting large, start a new one
            if (strlen($currentChunk) > 500000) { // 500KB chunks
                $sqlChunks[] = $currentChunk;
                $currentChunk = '';
            }
        }

        // Add the last chunk if not empty
        if (!empty($currentChunk)) {
            $sqlChunks[] = $currentChunk;
        }

        // Execute each chunk
        $query_count = 0;
        foreach ($sqlChunks as $chunk) {
            // Make sure we're still using the right database
            $chunk = "USE `{$db_config['database']}`;\n" . $chunk;

            // Execute the chunk
            $result = $mysqli->multi_query($chunk);

            if (!$result) {
                throw new Exception("Error importing database: " . $mysqli->error);
            }

            // Process all result sets
            do {
                $query_count++;
                // Free the result set
                if ($result = $mysqli->store_result()) {
                    $result->free();
                }
            } while ($mysqli->more_results() && $mysqli->next_result());

            // Check for errors after each chunk
            if ($mysqli->error) {
                throw new Exception("Error during import: " . $mysqli->error);
            }

            // Ensure database is still selected after each chunk
            $result = $mysqli->query("SELECT DATABASE()");
            if ($result) {
                $row = $result->fetch_row();
                $current_db = $row[0] ?? 'None';
                $result->free();

                if (empty($current_db) || $current_db !== $db_config['database']) {
                    logMessage("Database selection lost, reselecting database", "warning");
                    if (!$mysqli->select_db($db_config['database'])) {
                        throw new Exception("Failed to reselect database: {$db_config['database']}. Error: " . $mysqli->error);
                    }
                }
            }
        }

        // Check for errors
        if ($mysqli->error) {
            logMessage("Error during import: " . $mysqli->error, 'error');
        } else {
            logMessage("Successfully executed all queries");
        }

        // Re-enable foreign key checks
        $mysqli->query("SET FOREIGN_KEY_CHECKS = 1");
        logMessage("Re-enabled foreign key checks");

        // Verify database is still selected
        $result = $mysqli->query("SELECT DATABASE()");
        if ($result) {
            $row = $result->fetch_row();
            logMessage("Active database: " . ($row[0] ?? 'None'));
            $result->free();
        } else {
            logMessage("Could not verify active database", "error");
        }

        // Check if products table exists and has data
        $result = $mysqli->query("SELECT COUNT(*) FROM products");
        if ($result) {
            $row = $result->fetch_row();
            $product_count = $row[0] ?? 0;
            $result->free();

            // If no products exist, insert default products
            if ($product_count == 0) {
                logMessage("No products found. Adding default products...");

                // Insert default products
                $default_products = "
                    INSERT INTO products (product_name, description, quantity, price, alert_level, image_path, category) VALUES
                    ('iPhone 13 Pro', 'Apple iPhone 13 Pro 128GB', 15, 550000.00, 5, 'images/products/13pro.jpg', 'Phones'),
                    ('iPhone 14', 'Apple iPhone 14 256GB', 10, 650000.00, 3, 'images/products/14pro.jpg', 'Phones'),
                    ('Samsung Galaxy S22', 'Samsung Galaxy S22 Ultra 256GB', 8, 480000.00, 3, 'images/products/s22.jpg', 'Phones'),
                    ('Samsung Galaxy Z Fold 4', 'Samsung Galaxy Z Fold 4 512GB', 5, 750000.00, 2, 'images/products/zfold4.jpg', 'Phones'),
                    ('PlayStation 5', 'Sony PlayStation 5 Digital Edition', 7, 350000.00, 3, 'images/products/playstation5.jpg', 'Gaming'),
                    ('TECNO CAMON 40', 'TECNO CAMON 40 128GB', 12, 450000.00, 4, 'images/products/tecno.jpg', 'Phones');
                ";

                if ($mysqli->query($default_products)) {
                    logMessage("Successfully added default products");

                    // Add initial stock transactions for these products
                    $admin_query = "SELECT user_id FROM users WHERE role = 'admin' LIMIT 1";
                    $admin_result = $mysqli->query($admin_query);
                    if ($admin_result && $admin_result->num_rows > 0) {
                        $admin_row = $admin_result->fetch_assoc();
                        $admin_id = $admin_row['user_id'];

                        $stock_transactions = "
                            INSERT INTO stock_transactions (product_id, user_id, quantity, transaction_type, notes) VALUES
                            (1, $admin_id, 15, 'in', 'Initial stock'),
                            (2, $admin_id, 10, 'in', 'Initial stock'),
                            (3, $admin_id, 8, 'in', 'Initial stock'),
                            (4, $admin_id, 5, 'in', 'Initial stock'),
                            (5, $admin_id, 7, 'in', 'Initial stock'),
                            (6, $admin_id, 12, 'in', 'Initial stock');
                        ";

                        if ($mysqli->query($stock_transactions)) {
                            logMessage("Successfully added initial stock transactions");
                        } else {
                            logMessage("Failed to add initial stock transactions: " . $mysqli->error, "error");
                        }

                        $admin_result->free();
                    }
                } else {
                    logMessage("Failed to add default products: " . $mysqli->error, "error");
                }
            } else {
                logMessage("Found $product_count existing products. Skipping default product creation.");
            }
        }

        logMessage("Executed approximately $query_count queries successfully");

        // Close connection
        $mysqli->close();

        $success = true;
        $message = "Database imported successfully! You need to set up an admin account next.";

    } catch (Exception $e) {
        $error = true;
        $message = "Error: " . $e->getMessage();
        logMessage($e->getMessage(), 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Griffin Gadgets - Database Import</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --light-color: #f0f5fa;
            --dark-color: #2c3e50;
        }

        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background-color: var(--dark-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand img {
            height: 40px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .main-content {
            flex: 1;
            padding: 2rem 0;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-weight: 600;
            padding: 1.25rem;
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #27ae60;
            border-color: #27ae60;
        }

        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 1rem;
            font-family: monospace;
        }

        .log-info {
            color: var(--secondary-color);
        }

        .log-error {
            color: var(--accent-color);
        }

        .log-success {
            color: var(--success-color);
        }

        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 1.5rem 0;
            margin-top: auto;
        }

        .step-container {
            position: relative;
            padding-left: 30px;
            margin-bottom: 1.5rem;
        }

        .step-number {
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .progress-container {
            height: 5px;
            background-color: #e9ecef;
            border-radius: 3px;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
            width: 0;
            transition: width 0.5s ease;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#">
                <img src="images/logo.jpg" alt="Griffin Gadgets Logo" class="me-2">
                <span>Griffin Gadgets</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <?php if ($success): ?>
                        <div class="card mb-4">
                            <div class="card-body text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-check-circle text-success fa-5x"></i>
                                </div>
                                <h2 class="mb-3">Database Imported Successfully!</h2>
                                <p class="lead mb-4">Your Griffin Gadgets Inventory System is now ready to use.</p>
                                <div class="d-grid gap-2 col-md-6 mx-auto">
                                    <a href="adminsetup.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-user-shield me-2"></i>Set Up Admin Account
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php elseif ($error): ?>
                        <div class="card mb-4">
                            <div class="card-body text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-exclamation-triangle text-danger fa-5x"></i>
                                </div>
                                <h2 class="mb-3">Import Failed</h2>
                                <p class="lead mb-4"><?php echo $message; ?></p>
                                <div class="alert alert-warning">
                                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Common Solutions:</h5>
                                    <ul class="mb-0 text-start">
                                        <li>Make sure MySQL service is running in XAMPP Control Panel</li>
                                        <li>Check that the MySQL user (default: root) has permission to create databases</li>
                                        <li>Verify that the database/griffin_inventory.sql file exists in the correct location</li>
                                        <li>If using a different MySQL username or password, update the $db_config in importdb.php</li>
                                        <li>Try restarting the MySQL service in XAMPP Control Panel</li>
                                    </ul>
                                </div>
                                <div class="d-grid gap-2 col-md-6 mx-auto">
                                    <a href="importdb.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-redo me-2"></i>Try Again
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4 class="mb-0"><i class="fas fa-database me-2"></i>Database Import Wizard</h4>
                            </div>
                            <div class="card-body p-4">
                                <div class="text-center mb-4">
                                    <img src="images/logo.jpg" alt="Griffin Gadgets Logo" style="height: 80px; border-radius: 50%; border: 2px solid white;" class="mb-3">
                                    <h2>Griffin Gadgets Inventory System</h2>
                                    <p class="text-muted">One-Click Database Setup</p>
                                </div>

                                <div class="alert alert-info">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-info-circle fa-2x"></i>
                                        </div>
                                        <div>
                                            <h5 class="alert-heading">Before You Begin</h5>
                                            <p class="mb-0">Make sure you have XAMPP running with MySQL service active. This wizard will create the database and import all necessary tables for the inventory system.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h5 class="mb-3">Installation Steps:</h5>

                                    <div class="step-container">
                                        <div class="step-number">1</div>
                                        <h6>Database Creation</h6>
                                        <p class="text-muted mb-0">The wizard will create a new database called "griffin_inventory"</p>
                                    </div>

                                    <div class="step-container">
                                        <div class="step-number">2</div>
                                        <h6>Table Structure</h6>
                                        <p class="text-muted mb-0">All necessary tables will be created with proper relationships</p>
                                    </div>

                                    <div class="step-container">
                                        <div class="step-number">3</div>
                                        <h6>Sample Data</h6>
                                        <p class="text-muted mb-0">Sample products and users will be imported for testing</p>
                                    </div>
                                </div>

                                <form method="post" action="">
                                    <div class="d-grid">
                                        <button type="submit" name="import" class="btn btn-primary btn-lg pulse">
                                            <i class="fas fa-database me-2"></i>Import Database Now
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>


                    <?php endif; ?>

                    <?php if (!empty($log)): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-terminal me-2"></i>Import Log</h5>
                            </div>
                            <div class="card-body">
                                <div class="log-container">
                                    <?php foreach ($log as $entry): ?>
                                        <div class="log-<?php echo $entry['type']; ?>">
                                            <i class="fas fa-<?php echo $entry['type'] === 'error' ? 'times-circle' : ($entry['type'] === 'success' ? 'check-circle' : 'info-circle'); ?> me-2"></i>
                                            <?php echo $entry['message']; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container text-center">
            <p class="mb-0">Griffin Gadgets | 37a St. Michaels Aba | +234-810-079-5854 | <EMAIL></p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Password Toggle Script -->
    <script src="js/password-toggle.js"></script>
    <?php if (!empty($log)): ?>
    <script>
        // Auto-scroll to bottom of log container
        document.addEventListener('DOMContentLoaded', function() {
            const logContainer = document.querySelector('.log-container');
            if (logContainer) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        });
    </script>
    <?php endif; ?>
</body>
</html>
