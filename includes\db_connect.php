<?php
// Griffin Gadgets E-Commerce Platform Database Connection
// Enhanced from inventory system for e-commerce functionality

// Database connection parameters
$host = 'localhost';
$dbname = 'griffin_ecommerce';
$username = 'root';
$password = '';

// Create connection
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Set default fetch mode to associative array
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    // Enable emulated prepared statements for better compatibility
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch(PDOException $e) {
    // Log the error for debugging (in production, don't show detailed error messages)
    error_log("Database connection failed: " . $e->getMessage());
    die("ERROR: Could not connect to database. Please check your configuration.");
}

// Set timezone for consistent datetime handling
date_default_timezone_set('Africa/Lagos');

// Define constants for the application
define('SITE_NAME', 'Griffin Gadgets');
define('SITE_URL', 'http://localhost/GRIFFIN');
define('CONTACT_PHONE', '+234-************');
define('BUSINESS_ADDRESS', 'No. 37a St. Michaels Road');
define('CURRENCY_SYMBOL', '₦');
define('CURRENCY_CODE', 'NGN');

// Paystack configuration (Test Mode)
define('PAYSTACK_SECRET_KEY', 'sk_test_d3b85e05c9efd3da29ee9084881f88240427bc92');
define('PAYSTACK_PUBLIC_KEY', 'pk_test_42587f641841640bb4ad41e02d75084c31486578');
define('PAYSTACK_BASE_URL', 'https://api.paystack.co');

// Application settings
define('ITEMS_PER_PAGE', 12);
define('MAX_CART_ITEMS', 50);
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('ORDER_NUMBER_PREFIX', 'GG');

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('UPLOAD_PATH', 'images/products/');

// Email settings (for future implementation)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Griffin Gadgets');
?>
