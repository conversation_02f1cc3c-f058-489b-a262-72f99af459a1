<?php
// Griffin Gadgets E-Commerce Platform Core Functions
// Enhanced from inventory system for e-commerce functionality

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// ==================== AUTHENTICATION FUNCTIONS ====================

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Function to check if user is sales representative
function isSalesRep() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
}

// Function to check if user is customer
function isCustomer() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'customer';
}

// Function to redirect user if not logged in
function requireLogin($redirect_url = null) {
    if (!isLoggedIn()) {
        $redirect = $redirect_url ?: 'login.php';
        header("Location: $redirect");
        exit;
    }
}

// Function to redirect user if not admin
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header("Location: index.php?error=unauthorized");
        exit;
    }
}

// Function to redirect user if not sales rep
function requireSalesRep() {
    requireLogin();
    if (!isSalesRep()) {
        header("Location: index.php?error=unauthorized");
        exit;
    }
}

// Function to redirect user if not customer
function requireCustomer() {
    requireLogin();
    if (!isCustomer()) {
        header("Location: index.php?error=unauthorized");
        exit;
    }
}

// Function to check if user account is active
function isAccountActive() {
    return isset($_SESSION['status']) && $_SESSION['status'] === 'active';
}

// ==================== UTILITY FUNCTIONS ====================

// Function to sanitize input data
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Function to format currency
function formatCurrency($amount, $show_symbol = true) {
    $formatted = number_format($amount, 2);
    return $show_symbol ? CURRENCY_SYMBOL . $formatted : $formatted;
}

// Function to generate unique order number
function generateOrderNumber() {
    return ORDER_NUMBER_PREFIX . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

// Function to generate secure random string
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Function to generate alert badge based on stock level
function getStockStatusBadge($quantity, $alert_level = 5) {
    if ($quantity <= 0) {
        return '<span class="badge bg-danger">Out of Stock</span>';
    } elseif ($quantity <= $alert_level) {
        return '<span class="badge bg-warning text-dark">Low Stock</span>';
    } else {
        return '<span class="badge bg-success">In Stock</span>';
    }
}

// Function to get product by ID
function getProductById($pdo, $product_id) {
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    return $stmt->fetch();
}

// Function to log stock transaction
function logStockTransaction($pdo, $product_id, $user_id, $transaction_type, $quantity, $notes = '') {
    // Verify user exists in database
    $check_user = $pdo->prepare("SELECT user_id FROM users WHERE user_id = ?");
    $check_user->execute([$user_id]);

    if ($check_user->rowCount() > 0) {
        // User exists, proceed with transaction
        $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, ?, ?, ?)");
        return $stmt->execute([$product_id, $user_id, $transaction_type, $quantity, $notes]);
    } else {
        // User doesn't exist, use a default admin user from the database
        $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
        $admin = $get_admin->fetch();

        if ($admin && isset($admin['user_id'])) {
            $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, ?, ?, ?)");
            return $stmt->execute([$product_id, $admin['user_id'], $transaction_type, $quantity, $notes . ' (system)']);
        } else {
            // No admin found, just update the product quantity without logging
            error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
            return false;
        }
    }
}

// Function to update product quantity
function updateProductQuantity($pdo, $product_id, $new_quantity) {
    $stmt = $pdo->prepare("UPDATE products SET quantity = ? WHERE product_id = ?");
    return $stmt->execute([$new_quantity, $product_id]);
}

// Function to get low stock products
function getLowStockProducts($pdo) {
    $stmt = $pdo->query("SELECT * FROM products WHERE quantity <= alert_level ORDER BY quantity ASC");
    return $stmt->fetchAll();
}

// Function to get recent transactions
function getRecentTransactions($pdo, $limit = 10) {
    $stmt = $pdo->prepare("
        SELECT t.*, p.product_name, u.username
        FROM stock_transactions t
        JOIN products p ON t.product_id = p.product_id
        JOIN users u ON t.user_id = u.user_id
        ORDER BY t.transaction_date DESC
        LIMIT :limit
    ");
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll();
}

// Function to get image URL with proper path handling
function getImageUrl($imagePath) {
    // If the path is empty, return a placeholder
    if (empty($imagePath)) {
        return 'https://via.placeholder.com/300x200?text=No+Image';
    }

    // If the path is a full URL (starts with http:// or https://), return it as is
    if (preg_match('/^https?:\/\//', $imagePath)) {
        return $imagePath;
    }

    // If the path is relative to the root (starts with /)
    if (substr($imagePath, 0, 1) === '/') {
        return $imagePath;
    }

    // If the path is a relative path
    // Check if we're in an admin or storekeeper subdirectory
    $currentPath = $_SERVER['PHP_SELF'];
    if (strpos($currentPath, '/admin/') !== false || strpos($currentPath, '/storekeeper/') !== false) {
        return '../' . $imagePath;
    }

    // Otherwise, we're in the root directory
    return $imagePath;
}

// Function to get daily sales summary
function getDailySalesSummary($pdo, $date = null) {
    if ($date === null) {
        $date = date('Y-m-d');
    }

    $stmt = $pdo->prepare("
        SELECT p.product_id, p.product_name, SUM(t.quantity) as total_quantity, SUM(t.quantity * p.price) as total_value
        FROM stock_transactions t
        JOIN products p ON t.product_id = p.product_id
        WHERE t.transaction_type = 'out'
        AND DATE(t.transaction_date) = ?
        GROUP BY p.product_id, p.product_name
        ORDER BY total_value DESC
    ");
    $stmt->execute([$date]);
    return $stmt->fetchAll();
}

// Function to get CSS class for stock level display
function getStockLevelClass($quantity, $alert_level) {
    if ($quantity <= 0) {
        return 'text-danger';
    } elseif ($quantity <= $alert_level) {
        return 'text-warning';
    } else {
        return 'text-success';
    }
}

// Function to log system actions
function logAction($pdo, $action_type, $description, $user_id = null) {
    // If no user_id is provided, use the current logged-in user
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    // If still no user_id, try to get an admin user
    if ($user_id === null) {
        $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
        $admin = $get_admin->fetch();

        if ($admin && isset($admin['user_id'])) {
            $user_id = $admin['user_id'];
        } else {
            // No user found, log the action without a user ID
            error_log("Warning: No valid user found for action log: $action_type - $description");
            return false;
        }
    }

    // Create system_logs table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action_type VARCHAR(50) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            log_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
        )
    ");

    // Insert the log entry
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action_type, description, ip_address)
        VALUES (?, ?, ?, ?)
    ");

    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

    return $stmt->execute([$user_id, $action_type, $description, $ip]);
}

// Function to generate stock book report data
function getStockBookReport($pdo, $start_date, $end_date) {
    // Format dates for SQL query
    $start_date = date('Y-m-d 00:00:00', strtotime($start_date));
    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));

    // Get all products
    $products_stmt = $pdo->query("SELECT * FROM products ORDER BY product_name");
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

    $report_data = [];

    foreach ($products as $product) {
        $product_id = $product['product_id'];

        // Get opening stock (stock at start_date)
        $opening_stmt = $pdo->prepare("
            SELECT
                COALESCE(
                    (SELECT quantity FROM products WHERE product_id = :product_id) -
                    COALESCE(SUM(CASE WHEN transaction_type = 'in' THEN quantity ELSE 0 END), 0) +
                    COALESCE(SUM(CASE WHEN transaction_type = 'out' THEN quantity ELSE 0 END), 0),
                    0
                ) as opening_stock
            FROM stock_transactions
            WHERE product_id = :product_id AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $opening_stmt->bindParam(':product_id', $product_id);
        $opening_stmt->bindParam(':start_date', $start_date);
        $opening_stmt->bindParam(':end_date', $end_date);
        $opening_stmt->execute();
        $opening_stock = $opening_stmt->fetch(PDO::FETCH_ASSOC)['opening_stock'];

        // Get stock received during period
        $received_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as received
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'in'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $received_stmt->bindParam(':product_id', $product_id);
        $received_stmt->bindParam(':start_date', $start_date);
        $received_stmt->bindParam(':end_date', $end_date);
        $received_stmt->execute();
        $received = $received_stmt->fetch(PDO::FETCH_ASSOC)['received'];

        // Get stock sold during period
        $sold_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as sold
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'out'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $sold_stmt->bindParam(':product_id', $product_id);
        $sold_stmt->bindParam(':start_date', $start_date);
        $sold_stmt->bindParam(':end_date', $end_date);
        $sold_stmt->execute();
        $sold = $sold_stmt->fetch(PDO::FETCH_ASSOC)['sold'];

        // Calculate closing stock
        $closing_stock = $opening_stock + $received - $sold;

        // Add to report data
        $report_data[] = [
            'product_id' => $product_id,
            'product_name' => $product['product_name'],
            'category' => $product['category'],
            'price' => $product['price'],
            'opening_stock' => $opening_stock,
            'received' => $received,
            'sold' => $sold,
            'closing_stock' => $closing_stock,
            'closing_value' => $closing_stock * $product['price']
        ];
    }

    return $report_data;
}
?>
