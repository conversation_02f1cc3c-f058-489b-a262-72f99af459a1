<?php
// Griffin Gadgets E-Commerce Platform Core Functions
// Enhanced from inventory system for e-commerce functionality

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// ==================== AUTHENTICATION FUNCTIONS ====================

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Function to check if user is sales representative
function isSalesRep() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
}

// Function to check if user is customer
function isCustomer() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'customer';
}

// Function to redirect user if not logged in
function requireLogin($redirect_url = null) {
    if (!isLoggedIn()) {
        $redirect = $redirect_url ?: 'login.php';
        header("Location: $redirect");
        exit;
    }
}

// Function to redirect user if not admin
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header("Location: index.php?error=unauthorized");
        exit;
    }
}

// Function to redirect user if not sales rep
function requireSalesRep() {
    requireLogin();
    if (!isSalesRep()) {
        header("Location: index.php?error=unauthorized");
        exit;
    }
}

// Function to redirect user if not customer
function requireCustomer() {
    requireLogin();
    if (!isCustomer()) {
        header("Location: index.php?error=unauthorized");
        exit;
    }
}

// Function to check if user account is active
function isAccountActive() {
    return isset($_SESSION['status']) && $_SESSION['status'] === 'active';
}

// ==================== UTILITY FUNCTIONS ====================

// Function to sanitize input data
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Function to format currency
function formatCurrency($amount, $show_symbol = true) {
    $formatted = number_format($amount, 2);
    return $show_symbol ? CURRENCY_SYMBOL . $formatted : $formatted;
}

// Function to generate unique order number
function generateOrderNumber() {
    return ORDER_NUMBER_PREFIX . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

// Function to generate secure random string
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Function to generate alert badge based on stock level
function getStockStatusBadge($quantity, $alert_level = 5) {
    if ($quantity <= 0) {
        return '<span class="badge bg-danger">Out of Stock</span>';
    } elseif ($quantity <= $alert_level) {
        return '<span class="badge bg-warning text-dark">Low Stock</span>';
    } else {
        return '<span class="badge bg-success">In Stock</span>';
    }
}

// Function to check if product is in stock
function isInStock($quantity) {
    return $quantity > 0;
}

// ==================== PRODUCT FUNCTIONS ====================

// Function to get product by ID
function getProductById($pdo, $product_id) {
    $stmt = $pdo->prepare("
        SELECT p.*, c.category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE p.product_id = ? AND p.status = 'active'
    ");
    $stmt->execute([$product_id]);
    return $stmt->fetch();
}

// Function to get all active products with pagination
function getProducts($pdo, $page = 1, $limit = null, $category_id = null, $search = null) {
    $limit = $limit ?: ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;

    $where_conditions = ["p.status = 'active'"];
    $params = [];

    if ($category_id) {
        $where_conditions[] = "p.category_id = ?";
        $params[] = $category_id;
    }

    if ($search) {
        $where_conditions[] = "(p.product_name LIKE ? OR p.description LIKE ? OR p.tags LIKE ?)";
        $search_term = "%$search%";
        $params = array_merge($params, [$search_term, $search_term, $search_term]);
    }

    $where_clause = implode(' AND ', $where_conditions);

    $stmt = $pdo->prepare("
        SELECT p.*, c.category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE $where_clause
        ORDER BY p.featured DESC, p.created_at DESC
        LIMIT ? OFFSET ?
    ");

    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);

    return $stmt->fetchAll();
}

// Function to get product count for pagination
function getProductCount($pdo, $category_id = null, $search = null) {
    $where_conditions = ["status = 'active'"];
    $params = [];

    if ($category_id) {
        $where_conditions[] = "category_id = ?";
        $params[] = $category_id;
    }

    if ($search) {
        $where_conditions[] = "(product_name LIKE ? OR description LIKE ? OR tags LIKE ?)";
        $search_term = "%$search%";
        $params = array_merge($params, [$search_term, $search_term, $search_term]);
    }

    $where_clause = implode(' AND ', $where_conditions);

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE $where_clause");
    $stmt->execute($params);

    return $stmt->fetchColumn();
}

// Function to get featured products
function getFeaturedProducts($pdo, $limit = 6) {
    $stmt = $pdo->prepare("
        SELECT p.*, c.category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE p.status = 'active' AND p.featured = TRUE AND p.quantity > 0
        ORDER BY p.created_at DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

// ==================== SHOPPING CART FUNCTIONS ====================

// Function to add item to cart
function addToCart($pdo, $user_id, $product_id, $quantity = 1) {
    // Check if product exists and is in stock
    $product = getProductById($pdo, $product_id);
    if (!$product || !isInStock($product['quantity'])) {
        return false;
    }

    // Check if item already exists in cart
    $stmt = $pdo->prepare("SELECT * FROM shopping_cart WHERE user_id = ? AND product_id = ?");
    $stmt->execute([$user_id, $product_id]);
    $existing_item = $stmt->fetch();

    if ($existing_item) {
        // Update quantity
        $new_quantity = $existing_item['quantity'] + $quantity;
        $stmt = $pdo->prepare("UPDATE shopping_cart SET quantity = ?, updated_at = NOW() WHERE cart_id = ?");
        return $stmt->execute([$new_quantity, $existing_item['cart_id']]);
    } else {
        // Add new item
        $stmt = $pdo->prepare("INSERT INTO shopping_cart (user_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
        return $stmt->execute([$user_id, $product_id, $quantity, $product['price']]);
    }
}

// Function to get cart items for user
function getCartItems($pdo, $user_id) {
    $stmt = $pdo->prepare("
        SELECT c.*, p.product_name, p.image_path, p.quantity as stock_quantity
        FROM shopping_cart c
        JOIN products p ON c.product_id = p.product_id
        WHERE c.user_id = ? AND p.status = 'active'
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

// Function to get cart total
function getCartTotal($pdo, $user_id) {
    $stmt = $pdo->prepare("
        SELECT SUM(c.quantity * c.price) as total
        FROM shopping_cart c
        JOIN products p ON c.product_id = p.product_id
        WHERE c.user_id = ? AND p.status = 'active'
    ");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    return $result['total'] ?: 0;
}

// Function to update cart item quantity
function updateCartItem($pdo, $cart_id, $quantity) {
    if ($quantity <= 0) {
        return removeCartItem($pdo, $cart_id);
    }

    $stmt = $pdo->prepare("UPDATE shopping_cart SET quantity = ?, updated_at = NOW() WHERE cart_id = ?");
    return $stmt->execute([$quantity, $cart_id]);
}

// Function to remove item from cart
function removeCartItem($pdo, $cart_id) {
    $stmt = $pdo->prepare("DELETE FROM shopping_cart WHERE cart_id = ?");
    return $stmt->execute([$cart_id]);
}

// Function to clear cart
function clearCart($pdo, $user_id) {
    $stmt = $pdo->prepare("DELETE FROM shopping_cart WHERE user_id = ?");
    return $stmt->execute([$user_id]);
}

// ==================== ORDER FUNCTIONS ====================

// Function to create order from cart
function createOrderFromCart($pdo, $user_id, $shipping_address = null, $notes = null) {
    try {
        $pdo->beginTransaction();

        // Get cart items
        $cart_items = getCartItems($pdo, $user_id);
        if (empty($cart_items)) {
            throw new Exception('Cart is empty');
        }

        // Calculate totals
        $subtotal = getCartTotal($pdo, $user_id);
        $total = $subtotal; // Add tax/shipping if needed

        // Generate order number
        $order_number = generateOrderNumber();

        // Create order
        $stmt = $pdo->prepare("
            INSERT INTO orders (order_number, customer_id, subtotal, total_amount, shipping_address, customer_notes)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$order_number, $user_id, $subtotal, $total, $shipping_address, $notes]);
        $order_id = $pdo->lastInsertId();

        // Add order items and update stock
        foreach ($cart_items as $item) {
            // Add order item
            $stmt = $pdo->prepare("
                INSERT INTO order_items (order_id, product_id, product_name, product_sku, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $order_id,
                $item['product_id'],
                $item['product_name'],
                '', // SKU will be added later
                $item['quantity'],
                $item['price'],
                $item['quantity'] * $item['price']
            ]);

            // Update product stock
            $stmt = $pdo->prepare("UPDATE products SET quantity = quantity - ? WHERE product_id = ?");
            $stmt->execute([$item['quantity'], $item['product_id']]);

            // Log stock transaction
            logStockTransaction($pdo, $item['product_id'], $user_id, 'out', $item['quantity'], "Order #$order_number", $order_id);
        }

        // Clear cart
        clearCart($pdo, $user_id);

        $pdo->commit();
        return $order_id;

    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Order creation failed: " . $e->getMessage());
        return false;
    }
}

// Function to get order by ID
function getOrderById($pdo, $order_id, $user_id = null) {
    $sql = "SELECT * FROM orders WHERE order_id = ?";
    $params = [$order_id];

    if ($user_id) {
        $sql .= " AND customer_id = ?";
        $params[] = $user_id;
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetch();
}

// Function to get order items
function getOrderItems($pdo, $order_id) {
    $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ? ORDER BY item_id");
    $stmt->execute([$order_id]);
    return $stmt->fetchAll();
}

// ==================== STOCK TRANSACTION FUNCTIONS ====================

// Enhanced function to log stock transaction
function logStockTransaction($pdo, $product_id, $user_id, $transaction_type, $quantity, $notes = '', $order_id = null) {
    $stmt = $pdo->prepare("
        INSERT INTO stock_transactions (product_id, user_id, order_id, transaction_type, quantity, notes, reason)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");

    $reason = $transaction_type === 'out' ? 'sale' : 'purchase';
    return $stmt->execute([$product_id, $user_id, $order_id, $transaction_type, $quantity, $notes, $reason]);
}

// Function to update product quantity
function updateProductQuantity($pdo, $product_id, $new_quantity) {
    $stmt = $pdo->prepare("UPDATE products SET quantity = ? WHERE product_id = ?");
    return $stmt->execute([$new_quantity, $product_id]);
}

// Function to get low stock products
function getLowStockProducts($pdo) {
    $stmt = $pdo->query("SELECT * FROM products WHERE quantity <= alert_level AND status = 'active' ORDER BY quantity ASC");
    return $stmt->fetchAll();
}

// Function to get recent transactions
function getRecentTransactions($pdo, $limit = 10) {
    $stmt = $pdo->prepare("
        SELECT t.*, p.product_name, u.username, u.full_name
        FROM stock_transactions t
        JOIN products p ON t.product_id = p.product_id
        JOIN users u ON t.user_id = u.user_id
        ORDER BY t.transaction_date DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

// ==================== IMAGE FUNCTIONS ====================

// Enhanced function to get image URL with proper path handling
function getImageUrl($imagePath, $default = null) {
    // If the path is empty, return a placeholder
    if (empty($imagePath)) {
        return $default ?: 'https://via.placeholder.com/300x200?text=No+Image';
    }

    // If the path is a full URL (starts with http:// or https://), return it as is
    if (preg_match('/^https?:\/\//', $imagePath)) {
        return $imagePath;
    }

    // If the path is relative to the root (starts with /)
    if (substr($imagePath, 0, 1) === '/') {
        return $imagePath;
    }

    // If the path is a relative path
    // Check if we're in a subdirectory
    $currentPath = $_SERVER['PHP_SELF'];
    if (strpos($currentPath, '/admin/') !== false ||
        strpos($currentPath, '/sales_rep/') !== false ||
        strpos($currentPath, '/customer/') !== false) {
        return '../' . $imagePath;
    }

    // Otherwise, we're in the root directory
    return $imagePath;
}

// Function to validate uploaded image
function validateImage($file) {
    $errors = [];

    // Check if file was uploaded
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = 'File upload failed.';
        return $errors;
    }

    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        $errors[] = 'File size exceeds maximum allowed size.';
    }

    // Check file type
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, ALLOWED_IMAGE_TYPES)) {
        $errors[] = 'Invalid file type. Allowed types: ' . implode(', ', ALLOWED_IMAGE_TYPES);
    }

    // Check if it's actually an image
    $image_info = getimagesize($file['tmp_name']);
    if ($image_info === false) {
        $errors[] = 'File is not a valid image.';
    }

    return $errors;
}

// ==================== CATEGORY FUNCTIONS ====================

// Function to get all active categories
function getCategories($pdo) {
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY category_name");
    return $stmt->fetchAll();
}

// Function to get category by ID
function getCategoryById($pdo, $category_id) {
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE category_id = ? AND status = 'active'");
    $stmt->execute([$category_id]);
    return $stmt->fetch();
}

// ==================== USER FUNCTIONS ====================

// Function to get user by ID
function getUserById($pdo, $user_id) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

// Function to verify security answers
function verifySecurityAnswers($pdo, $user_id, $answer1, $answer2) {
    $user = getUserById($pdo, $user_id);
    if (!$user) return false;

    return password_verify($answer1, $user['security_answer_1']) &&
           password_verify($answer2, $user['security_answer_2']);
}

// Function to get daily sales summary
function getDailySalesSummary($pdo, $date = null) {
    if ($date === null) {
        $date = date('Y-m-d');
    }

    $stmt = $pdo->prepare("
        SELECT p.product_id, p.product_name, SUM(t.quantity) as total_quantity, SUM(t.quantity * p.price) as total_value
        FROM stock_transactions t
        JOIN products p ON t.product_id = p.product_id
        WHERE t.transaction_type = 'out'
        AND DATE(t.transaction_date) = ?
        GROUP BY p.product_id, p.product_name
        ORDER BY total_value DESC
    ");
    $stmt->execute([$date]);
    return $stmt->fetchAll();
}

// Function to get CSS class for stock level display
function getStockLevelClass($quantity, $alert_level) {
    if ($quantity <= 0) {
        return 'text-danger';
    } elseif ($quantity <= $alert_level) {
        return 'text-warning';
    } else {
        return 'text-success';
    }
}

// Function to log system actions
function logAction($pdo, $action_type, $description, $user_id = null) {
    // If no user_id is provided, use the current logged-in user
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    // If still no user_id, try to get an admin user
    if ($user_id === null) {
        $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
        $admin = $get_admin->fetch();

        if ($admin && isset($admin['user_id'])) {
            $user_id = $admin['user_id'];
        } else {
            // No user found, log the action without a user ID
            error_log("Warning: No valid user found for action log: $action_type - $description");
            return false;
        }
    }

    // Create system_logs table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action_type VARCHAR(50) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            log_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
        )
    ");

    // Insert the log entry
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action_type, description, ip_address)
        VALUES (?, ?, ?, ?)
    ");

    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

    return $stmt->execute([$user_id, $action_type, $description, $ip]);
}

// Function to generate stock book report data
function getStockBookReport($pdo, $start_date, $end_date) {
    // Format dates for SQL query
    $start_date = date('Y-m-d 00:00:00', strtotime($start_date));
    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));

    // Get all products
    $products_stmt = $pdo->query("SELECT * FROM products ORDER BY product_name");
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

    $report_data = [];

    foreach ($products as $product) {
        $product_id = $product['product_id'];

        // Get opening stock (stock at start_date)
        $opening_stmt = $pdo->prepare("
            SELECT
                COALESCE(
                    (SELECT quantity FROM products WHERE product_id = :product_id) -
                    COALESCE(SUM(CASE WHEN transaction_type = 'in' THEN quantity ELSE 0 END), 0) +
                    COALESCE(SUM(CASE WHEN transaction_type = 'out' THEN quantity ELSE 0 END), 0),
                    0
                ) as opening_stock
            FROM stock_transactions
            WHERE product_id = :product_id AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $opening_stmt->bindParam(':product_id', $product_id);
        $opening_stmt->bindParam(':start_date', $start_date);
        $opening_stmt->bindParam(':end_date', $end_date);
        $opening_stmt->execute();
        $opening_stock = $opening_stmt->fetch(PDO::FETCH_ASSOC)['opening_stock'];

        // Get stock received during period
        $received_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as received
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'in'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $received_stmt->bindParam(':product_id', $product_id);
        $received_stmt->bindParam(':start_date', $start_date);
        $received_stmt->bindParam(':end_date', $end_date);
        $received_stmt->execute();
        $received = $received_stmt->fetch(PDO::FETCH_ASSOC)['received'];

        // Get stock sold during period
        $sold_stmt = $pdo->prepare("
            SELECT COALESCE(SUM(quantity), 0) as sold
            FROM stock_transactions
            WHERE product_id = :product_id
            AND transaction_type = 'out'
            AND transaction_date BETWEEN :start_date AND :end_date
        ");
        $sold_stmt->bindParam(':product_id', $product_id);
        $sold_stmt->bindParam(':start_date', $start_date);
        $sold_stmt->bindParam(':end_date', $end_date);
        $sold_stmt->execute();
        $sold = $sold_stmt->fetch(PDO::FETCH_ASSOC)['sold'];

        // Calculate closing stock
        $closing_stock = $opening_stock + $received - $sold;

        // Add to report data
        $report_data[] = [
            'product_id' => $product_id,
            'product_name' => $product['product_name'],
            'category' => $product['category'],
            'price' => $product['price'],
            'opening_stock' => $opening_stock,
            'received' => $received,
            'sold' => $sold,
            'closing_stock' => $closing_stock,
            'closing_value' => $closing_stock * $product['price']
        ];
    }

    return $report_data;
}
?>
