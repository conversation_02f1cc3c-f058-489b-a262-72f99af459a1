<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Griffin Gadgets - Inventory Management System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo isset($is_admin) ? '../' : ''; ?>css/style.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
        }

        @keyframes blink {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(243, 156, 18, 0); }
            100% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0); }
        }

        .alert-nav-container {
            display: flex;
            align-items: center;
            margin-left: 5px;
        }

        /* Add tooltip functionality */
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1001;
        }

        .alert-nav-item {
            background-color: #f39c12;
            border-radius: 50%;
            list-style: none;
            width: 40px;
            height: 40px;
            overflow: hidden;
            animation: pulse 2s infinite;
            box-shadow: 0 0 0 rgba(243, 156, 18, 0.4);
            border: 2px solid #fff;
        }

        .alert-in-nav {
            color: white;
            padding: 0;
            text-align: center;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            position: relative;
            cursor: pointer;
            width: 100%;
            height: 100%;
        }

        .alert-triangle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }

        .blinking-exclamation {
            color: white;
            font-size: 24px;
            font-weight: bold;
            animation: blink 0.8s infinite;
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.7);
            display: inline-block;
        }

        .alert-count {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: red;
            color: white;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            font-size: 13px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            border: 2px solid white;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        }

        .alert-link {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
        }

        .alert-in-nav a {
            color: white !important;
            text-decoration: underline;
            margin-left: 5px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        .navbar {
            background-color: var(--secondary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand img {
            height: 40px;
            margin-right: 10px;
            border-radius: 50%;
            border: 2px solid white;
            padding: 2px;
            background-color: white;
        }

        .navbar-brand span {
            font-weight: 600;
            color: white;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: white !important;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item:hover {
            background-color: var(--light-color);
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-weight: bold;
        }

        .user-role {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .footer {
            background-color: var(--secondary-color);
            color: white;
            padding: 20px 0;
            margin-top: 50px;
        }

        .footer a {
            color: rgba(255, 255, 255, 0.8);
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
            text-decoration: none;
        }

        .social-icons a {
            display: inline-block;
            width: 36px;
            height: 36px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            text-align: center;
            line-height: 36px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-color);
            transform: translateY(-3px);
        }

        @media (max-width: 768px) {
            .navbar-brand img {
                height: 30px;
                border-radius: 50%;
                border: 2px solid white;
                padding: 2px;
                background-color: white;
            }

            .alert-container {
                position: static;
                margin: 10px 0;
                transform: none;
            }
        }
    </style>
</head>
<body>
    <?php
    // Check for low stock items if user is logged in
    $alert_html = '';
    if (isLoggedIn() && isset($pdo)) {
        // Check for low stock items - this will persist until stock is added

        // Check for products with quantity at or below alert level
        $low_stock_query = $pdo->query("SELECT COUNT(*) as count FROM products WHERE quantity <= alert_level AND quantity > 0");
        $low_stock_count = $low_stock_query ? $low_stock_query->fetch()['count'] : 0;

        // Check for out of stock products
        $out_of_stock_query = $pdo->query("SELECT COUNT(*) as count FROM products WHERE quantity = 0");
        $out_of_stock_count = $out_of_stock_query ? $out_of_stock_query->fetch()['count'] : 0;

        $total_alerts = $low_stock_count + $out_of_stock_count;

        // Only show the alert if there are low stock or out of stock items
        if ($total_alerts > 0) {
            $alert_html = '<div class="alert-nav-item" title="Click to view low stock items">';
            $alert_html .= '<div class="alert-in-nav">';
            $alert_html .= '<div class="alert-triangle"><span class="blinking-exclamation">!</span></div>';

            // Create a tooltip with the alert details
            $tooltip_content = '';
            if ($out_of_stock_count > 0) {
                $tooltip_content .= $out_of_stock_count . ' product(s) out of stock';
                if ($low_stock_count > 0) {
                    $tooltip_content .= ' and ' . $low_stock_count . ' product(s) running low!';
                } else {
                    $tooltip_content .= '!';
                }
            } else {
                $tooltip_content .= $low_stock_count . ' product(s) running low!';
            }

            // Add the count badge
            $alert_html .= '<span class="alert-count">' . $total_alerts . '</span>';

            // Add the link to view details
            if (isAdmin()) {
                $alert_html .= '<a href="' . (isset($is_admin) ? '' : 'admin/') . 'alerts.php" class="alert-link" title="' . htmlspecialchars($tooltip_content) . '"></a>';
            } else {
                $alert_html .= '<a href="' . (isset($is_storekeeper) ? '' : 'storekeeper/') . 'alerts.php" class="alert-link" title="' . htmlspecialchars($tooltip_content) . '"></a>';
            }

            $alert_html .= '</div>';
            $alert_html .= '</div>';
        }
    }
    ?>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>index.php">
                <img src="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>images/logo.jpg" alt="Griffin Gadgets Logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse position-relative" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>index.php">Home</a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>dashboard.php">Dashboard</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Inventory
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="inventoryDropdown">
                                    <li><a class="dropdown-item" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>products.php">Products</a></li>
                                    <li><a class="dropdown-item" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>stock-in.php">Stock In</a></li>
                                    <li><a class="dropdown-item" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>stock-out.php">Stock Out</a></li>
                                    <li><a class="dropdown-item" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>alerts.php">Stock Alerts</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Reports
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                                    <li><a class="dropdown-item" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>sales-report.php">Sales Report</a></li>
                                    <li><a class="dropdown-item" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>inventory-report.php">Inventory Report</a></li>
                                    <li><a class="dropdown-item" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>transactions.php">Transactions</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" href="<?php echo isset($is_admin) ? '' : 'admin/'; ?>users.php">
                                    <i class="fas fa-users me-1"></i> Users
                                    <?php
                                    // Get pending users count
                                    if (isset($pdo)) {
                                        $pending_query = $pdo->query("SELECT COUNT(*) as count FROM users WHERE status = 'pending'");
                                        $pending_count = $pending_query ? $pending_query->fetch()['count'] : 0;

                                        $reset_query = $pdo->query("SELECT COUNT(*) as count FROM users WHERE password_reset_requested = TRUE");
                                        $reset_count = $reset_query ? $reset_query->fetch()['count'] : 0;

                                        $total_notifications = $pending_count + $reset_count;

                                        if ($total_notifications > 0) {
                                            echo '<span class="badge rounded-pill bg-danger ms-1">' . $total_notifications . '</span>';
                                        }
                                    }
                                    ?>
                                </a>
                            </li>
                        <?php elseif (isStorekeeper()): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="<?php echo isset($is_storekeeper) ? '' : 'storekeeper/'; ?>dashboard.php">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'record-sale.php' ? 'active' : ''; ?>" href="<?php echo isset($is_storekeeper) ? '' : 'storekeeper/'; ?>record-sale.php">Record Sale</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'inventory.php' ? 'active' : ''; ?>" href="<?php echo isset($is_storekeeper) ? '' : 'storekeeper/'; ?>inventory.php">Inventory</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'alerts.php' ? 'active' : ''; ?>" href="<?php echo isset($is_storekeeper) ? '' : 'storekeeper/'; ?>alerts.php">Alerts</a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'about.php' ? 'active' : ''; ?>" href="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>" href="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>contact.php">Contact</a>
                    </li>
                    <?php if (!empty($alert_html)): ?>
                    <li class="nav-item alert-nav-container">
                        <?php echo $alert_html; ?>
                    </li>
                    <?php endif; ?>
                </ul>

                <div class="ms-auto d-flex">
                    <?php if (isLoggedIn()): ?>
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo substr($_SESSION['username'], 0, 1); ?>
                                    </div>
                                    <div>
                                        <div><?php echo $_SESSION['username']; ?></div>
                                        <div class="user-role"><?php echo ucfirst($_SESSION['role']); ?></div>
                                    </div>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>profile.php"><i class="fas fa-user-circle me-2"></i>Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>login.php" class="btn btn-outline-light me-2">Login</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <div class="container py-4">
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_GET['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_GET['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
