<?php
// Include header
include 'includes/header.php';

// Include database connection
require_once 'includes/db_connect.php';

// Get featured products with their quantities
$stmt = $pdo->query("SELECT * FROM products WHERE quantity > 0 ORDER BY quantity DESC LIMIT 5");
$featured_products = $stmt->fetchAll();
?>

<style>
    /* Custom styles for index page */
    body {
        background-color: #f0f5fa;
    }

    /* Professional heading styles */
    .text-gradient {
        background: linear-gradient(45deg, #f9d423, #ff4e50);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        display: inline-block;
        padding: 0 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        position: relative;
        font-size: 1.2em;
    }

    .text-gradient::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 10px;
        right: 10px;
        height: 3px;
        background: linear-gradient(45deg, #f9d423, #ff4e50);
        border-radius: 3px;
    }

    .inventory-text {
        color: #ffffff;
        display: block;
        font-weight: 300;
        letter-spacing: 2px;
        margin-top: 5px;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        position: relative;
        font-size: 0.8em;
        text-transform: uppercase;
    }

    @media (max-width: 768px) {
        .text-gradient {
            font-size: 1em;
        }

        .inventory-text {
            font-size: 0.7em;
        }
    }

    .hero-section {
        position: relative;
        overflow: hidden;
        padding: 6rem 0;
        background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/hero-bg.jpg') no-repeat center center;
        background-size: cover;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 3rem;
    }

    .hero-content {
        animation: fadeInUp 1s ease;
    }

    .feature-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        border-radius: 50%;
        transition: transform 0.3s ease;
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.1);
    }

    .product-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .product-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .product-card .card-img-top {
        height: 200px;
        object-fit: contain;
        padding: 1rem;
        background-color: white;
        transition: transform 0.3s ease;
    }

    .product-card:hover .card-img-top {
        transform: scale(1.05);
    }

    .card-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .cta-section {
        background-color: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin: 3rem 0;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    .animate-fadeIn {
        animation: fadeIn 1s ease;
    }

    .animate-fadeInUp {
        animation: fadeInUp 1s ease;
    }

    /* Improved responsiveness */
    @media (max-width: 768px) {
        .hero-section {
            padding: 4rem 0;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
        }
    }

    @media (max-width: 576px) {
        .hero-section {
            padding: 3rem 0;
        }

        .cta-section {
            padding: 2rem;
        }
    }
</style>

<!-- Hero Section -->
<div class="container-fluid px-0">
    <div class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center hero-content">
                    <h1 class="display-4 fw-bold mb-4 text-white">
                        <span class="text-gradient">Griffin Gadgets</span>
                        <span class="inventory-text">Inventory Management</span>
                    </h1>
                    <p class="lead mb-4 text-white">Streamline your inventory processes, reduce errors, and make better business decisions with our comprehensive inventory management system.</p>
                    <div class="d-flex flex-wrap justify-content-center gap-3 mt-4">
                        <?php if (!isLoggedIn()): ?>
                            <a href="login.php" class="btn btn-primary btn-lg px-4">Login Now</a>
                        <?php else: ?>
                            <?php if (isAdmin()): ?>
                                <a href="admin/dashboard.php" class="btn btn-primary btn-lg px-4">Admin Dashboard</a>
                            <?php else: ?>
                                <a href="storekeeper/dashboard.php" class="btn btn-primary btn-lg px-4">Storekeeper Dashboard</a>
                            <?php endif; ?>
                        <?php endif; ?>
                        <a href="about.php" class="btn btn-outline-light btn-lg px-4">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container py-5">
    <div class="row text-center mb-5 animate-fadeInUp">
        <div class="col-lg-8 mx-auto">
            <h2 class="fw-bold">Key Features</h2>
            <p class="lead text-muted">Our inventory management system is designed to help small businesses manage their stock efficiently.</p>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card feature-card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-primary text-white">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                    <h3 class="h5 fw-bold">Stock Management</h3>
                    <p class="text-muted">Keep track of your inventory levels in real-time. Add new products, update stock levels, and monitor stock movements.</p>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card feature-card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-success text-white">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                    <h3 class="h5 fw-bold">Sales Tracking</h3>
                    <p class="text-muted">Record daily sales and monitor product performance. Identify your best-selling products and optimize your inventory accordingly.</p>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card feature-card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-warning text-white">
                        <i class="fas fa-bell fa-2x"></i>
                    </div>
                    <h3 class="h5 fw-bold">Low Stock Alerts</h3>
                    <p class="text-muted">Get notified when your inventory reaches critical levels. Set custom alert thresholds for each product to avoid stockouts.</p>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card feature-card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-info text-white">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                    <h3 class="h5 fw-bold">Comprehensive Reports</h3>
                    <p class="text-muted">Generate detailed reports on sales, inventory levels, and stock movements to make informed business decisions.</p>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card feature-card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-danger text-white">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h3 class="h5 fw-bold">User Role Management</h3>
                    <p class="text-muted">Assign different roles to your team members. Admin can manage all aspects while storekeepers focus on daily operations.</p>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card feature-card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-secondary text-white">
                        <i class="fas fa-mobile-alt fa-2x"></i>
                    </div>
                    <h3 class="h5 fw-bold">Responsive Design</h3>
                    <p class="text-muted">Access your inventory system from any device - desktop, tablet, or mobile phone with our fully responsive design.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products Section -->
<div class="container py-5">
    <div class="container">
        <div class="row text-center mb-5 animate-fadeInUp">
            <div class="col-lg-8 mx-auto">
                <h2 class="fw-bold">Our Products</h2>
                <p class="lead text-muted">Griffin Gadgets specializes in high-quality phones and gaming products.</p>
            </div>
        </div>

        <div class="row g-4">
            <?php
            if (count($featured_products) > 0): ?>
                <?php foreach ($featured_products as $product):
                    // Get image path directly from the database
                    $image_path = !empty($product['image_path']) ? $product['image_path'] : '';
                ?>
                    <div class="col-sm-6 col-md-4 col-lg-3 mb-4">
                        <div class="card product-card h-100" data-product-id="<?php echo $product['product_id']; ?>">
                            <img src="<?php echo htmlspecialchars(getImageUrl($image_path)); ?>" class="card-img-top"
                                alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                                onerror="this.src='https://via.placeholder.com/300x200?text=<?php echo urlencode($product['product_name']); ?>'">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($product['product_name']); ?></h5>
                                <p class="card-price"><?php echo formatCurrency($product['price']); ?></p>
                                <p class="card-text"><?php echo htmlspecialchars(substr($product['description'], 0, 100) . (strlen($product['description']) > 100 ? '...' : '')); ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <?php echo getStockStatusBadge($product['quantity'], $product['alert_level']); ?>
                                    <span class="text-muted"><?php echo $product['quantity']; ?> units</span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No products available at the moment.
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Call to Action -->
<div class="container py-5">
    <div class="cta-section animate-fadeIn">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">Ready to streamline your inventory management?</h2>
                <p class="lead mb-4">Our system helps you keep track of your stock, monitor sales, and make better business decisions.</p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <?php if (!isLoggedIn()): ?>
                        <a href="login.php" class="btn btn-primary btn-lg px-5 py-3">Get Started Now</a>
                    <?php else: ?>
                        <?php if (isAdmin()): ?>
                            <a href="admin/dashboard.php" class="btn btn-primary btn-lg px-5 py-3">Go to Dashboard</a>
                        <?php else: ?>
                            <a href="storekeeper/dashboard.php" class="btn btn-primary btn-lg px-5 py-3">Go to Dashboard</a>
                        <?php endif; ?>
                    <?php endif; ?>
                    <a href="about.php" class="btn btn-outline-primary btn-lg px-5 py-3">Learn More</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>

<script>
    // Add animation to elements when they come into view
    document.addEventListener('DOMContentLoaded', function() {
        // Function to check if element is in viewport
        function isInViewport(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.bottom >= 0
            );
        }

        // Function to add animation class when element is in viewport
        function animateOnScroll() {
            const featureCards = document.querySelectorAll('.feature-card');
            const productCards = document.querySelectorAll('.product-card');

            featureCards.forEach(card => {
                if (isInViewport(card) && !card.classList.contains('animate-fadeInUp')) {
                    card.classList.add('animate-fadeInUp');
                    // Add a slight delay for each card to create a cascade effect
                    card.style.animationDelay = (Array.from(featureCards).indexOf(card) * 0.1) + 's';
                }
            });

            productCards.forEach(card => {
                if (isInViewport(card) && !card.classList.contains('animate-fadeInUp')) {
                    card.classList.add('animate-fadeInUp');
                    // Add a slight delay for each card to create a cascade effect
                    card.style.animationDelay = (Array.from(productCards).indexOf(card) * 0.1) + 's';
                }
            });
        }

        // Run on scroll and on load
        window.addEventListener('scroll', animateOnScroll);
        window.addEventListener('load', animateOnScroll);

        // Initial call
        animateOnScroll();

        // Real-time stock updates
        function updateStockInfo() {
            fetch('ajax/get-stock-updates.php')
                .then(response => response.json())
                .then(data => {
                    // Update stock information for each product card
                    const productCards = document.querySelectorAll('.product-card');

                    productCards.forEach(card => {
                        const productId = card.getAttribute('data-product-id');
                        if (productId) {
                            const product = data.find(p => p.product_id === productId);
                            if (product) {
                                // Update quantity display
                                const quantityElement = card.querySelector('.text-muted');
                                if (quantityElement) {
                                    quantityElement.textContent = `${product.quantity} units`;
                                }

                                // Update stock status badge
                                const badgeElement = card.querySelector('.badge');
                                if (badgeElement) {
                                    if (product.quantity <= 0) {
                                        badgeElement.className = 'badge bg-danger';
                                        badgeElement.textContent = 'Out of Stock';
                                    } else if (product.quantity <= product.alert_level) {
                                        badgeElement.className = 'badge bg-warning text-dark';
                                        badgeElement.textContent = 'Low Stock';
                                    } else {
                                        badgeElement.className = 'badge bg-success';
                                        badgeElement.textContent = 'In Stock';
                                    }
                                }
                            }
                        }
                    });
                })
                .catch(error => console.error('Error fetching stock updates:', error));
        }

        // Update stock info every 30 seconds
        setInterval(updateStockInfo, 30000);

        // Initial stock info update
        updateStockInfo();
    });
</script>
