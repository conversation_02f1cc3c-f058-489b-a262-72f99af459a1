=======================================================================
                GRIFFIN GADGETS INVENTORY SYSTEM
                      INSTALLATION GUIDE
=======================================================================

TABLE OF CONTENTS
----------------
1. System Requirements
2. Installation Steps
3. First-Time Setup
4. Troubleshooting
5. Upgrading
6. Backup and Restore

-----------------------------------------------------------------------
1. SYSTEM REQUIREMENTS
-----------------------------------------------------------------------

Before installing the Griffin Gadgets Inventory System, ensure your 
server meets the following requirements:

1.1 Server Requirements
    - Web server (Apache recommended)
    - PHP 7.4 or higher
    - MySQL 5.7 or higher
    - PDO PHP Extension
    - GD PHP Extension (for image processing)

1.2 Recommended Software
    - XAMPP (includes Apache, MySQL, PHP)
    - Web browser (Chrome, Firefox, Edge recommended)
    - Minimum screen resolution: 1280x720

-----------------------------------------------------------------------
2. INSTALLATION STEPS
-----------------------------------------------------------------------

Follow these steps to install the Griffin Gadgets Inventory System:

2.1 Install XAMPP
    - Download XAMPP from https://www.apachefriends.org/
    - Run the installer and follow the on-screen instructions
    - Install Apache, MySQL, and PHP components

2.2 Start XAMPP Services
    - Launch XAMPP Control Panel
    - Start Apache and MySQL services
    - Ensure both services are running (green status)

2.3 Copy System Files
    - Extract the INVENTORY folder from the provided zip file
    - Copy the entire INVENTORY folder to C:\xampp\htdocs\
    - Ensure all files and folders are copied correctly

2.4 Import Database
    - Open your web browser
    - Navigate to http://localhost/INVENTORY/importdb.php
    - Click the "Import Database Now" button
    - Wait for the confirmation message

2.5 Access the System
    - Navigate to http://localhost/INVENTORY/
    - Log in with the default administrator credentials
    - Username: admin
    - Password: admin123

-----------------------------------------------------------------------
3. FIRST-TIME SETUP
-----------------------------------------------------------------------

After installation, perform these first-time setup tasks:

3.1 Change Default Passwords
    - Log in as admin
    - Go to the profile section
    - Change the default password to a secure one

3.2 Configure System Settings
    - Update company information if needed
    - Verify product categories
    - Check alert level settings

3.3 Add Initial Products
    - Add your actual inventory products
    - Set appropriate stock levels
    - Configure alert thresholds

3.4 Create User Accounts
    - Create accounts for storekeepers
    - Assign appropriate permissions
    - Provide training on system usage

-----------------------------------------------------------------------
4. TROUBLESHOOTING
-----------------------------------------------------------------------

If you encounter issues during installation or operation:

4.1 Database Connection Issues
    - Verify MySQL service is running
    - Check database credentials in includes/db_connect.php
    - Ensure the griffin_inventory database exists

4.2 Page Not Found Errors
    - Verify Apache service is running
    - Check file permissions (read access required)
    - Ensure all files were copied correctly

4.3 Image Display Problems
    - Verify the images directory exists and has proper permissions
    - Check image file paths in the code
    - Ensure image files are in the correct format (JPG, PNG)

4.4 Login Issues
    - Clear browser cache and cookies
    - Verify username and password
    - Check user account status in the database

-----------------------------------------------------------------------
5. UPGRADING
-----------------------------------------------------------------------

When upgrading to a newer version:

5.1 Backup Current System
    - Export the database using phpMyAdmin
    - Create a backup copy of the INVENTORY folder

5.2 Install New Version
    - Follow the installation steps for the new version
    - Import your backed-up database if needed

5.3 Verify Functionality
    - Test all major features after upgrading
    - Check for any data migration issues

-----------------------------------------------------------------------
6. BACKUP AND RESTORE
-----------------------------------------------------------------------

Regular backups are essential for data security:

6.1 Database Backup
    - Open phpMyAdmin (http://localhost/phpmyadmin)
    - Select the griffin_inventory database
    - Click Export and choose SQL format
    - Save the exported file in a secure location

6.2 File Backup
    - Create a copy of the entire INVENTORY folder
    - Pay special attention to the images/products directory
    - Store backups in multiple locations

6.3 Restore Process
    - Import the database backup using phpMyAdmin
    - Replace the INVENTORY folder with your backup copy
    - Verify system functionality after restore

=======================================================================
                        END OF DOCUMENT
=======================================================================
