/**
 * Password Toggle Visibility - Fixed Version
 * This script adds eye icons to password fields to toggle visibility
 * without duplicating labels or breaking form layouts
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find all password input fields
    const passwordFields = document.querySelectorAll('input[type="password"]');
    
    // Add toggle button to each password field
    passwordFields.forEach(function(field) {
        // Skip if this field already has a password toggle button
        const parent = field.parentElement;
        if (parent.querySelector('.password-toggle')) {
            return;
        }
        
        // Create the toggle button
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.className = 'btn btn-outline-secondary password-toggle';
        toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        toggleButton.title = 'Show password';
        toggleButton.setAttribute('aria-label', 'Toggle password visibility');
        
        // Position the button absolutely to avoid changing the layout
        toggleButton.style.position = 'absolute';
        toggleButton.style.right = '0';
        toggleButton.style.top = '0';
        toggleButton.style.height = '100%';
        toggleButton.style.zIndex = '5';
        toggleButton.style.borderRadius = '0 0.375rem 0.375rem 0';
        
        // Make the parent position relative if it's not already
        if (getComputedStyle(parent).position === 'static') {
            parent.style.position = 'relative';
        }
        
        // Add padding to the right of the input field to make room for the button
        field.style.paddingRight = '40px';
        
        // Add the toggle button to the parent
        parent.appendChild(toggleButton);
        
        // Add click event to toggle password visibility
        toggleButton.addEventListener('click', function(e) {
            // Prevent the button from submitting forms
            e.preventDefault();
            
            // Toggle the password field type
            if (field.type === 'password') {
                field.type = 'text';
                toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
                toggleButton.title = 'Hide password';
            } else {
                field.type = 'password';
                toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
                toggleButton.title = 'Show password';
            }
        });
    });
    
    // Add some CSS for the toggle button
    const style = document.createElement('style');
    style.textContent = `
        .password-toggle {
            cursor: pointer;
            background-color: #e9ecef;
            border-color: #ced4da;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            padding: 0;
        }
        
        .password-toggle:hover {
            background-color: #dee2e6;
        }
        
        .password-toggle:focus {
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            z-index: 5;
        }
        
        /* Adjust input field to accommodate the button */
        input[type="password"], 
        input[type="text"].form-control[data-was-password="true"] {
            padding-right: 40px;
        }
    `;
    document.head.appendChild(style);
});
