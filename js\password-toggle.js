/**
 * Password Toggle Visibility
 * This script adds eye icons to password fields to toggle visibility
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find all password input fields
    const passwordFields = document.querySelectorAll('input[type="password"]');

    // Add toggle button to each password field
    passwordFields.forEach(function(field) {
        // Skip if this field already has a password toggle button
        if (field.parentElement.querySelector('.password-toggle')) {
            return;
        }

        // Create the toggle button
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.className = 'btn btn-outline-secondary password-toggle';
        toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        toggleButton.title = 'Show password';
        toggleButton.setAttribute('aria-label', 'Toggle password visibility');

        // Handle different form layouts
        let inputGroup;
        const parent = field.parentElement;

        // Check if already in an input group
        if (parent.classList.contains('input-group')) {
            inputGroup = parent;
        }
        // Handle Bootstrap floating labels
        else if (parent.classList.contains('form-floating')) {
            // Create a new input group
            inputGroup = document.createElement('div');
            inputGroup.className = 'input-group';

            // Get the label
            const label = parent.querySelector('label');

            // Move the field to the input group
            field.parentNode.insertBefore(inputGroup, field);
            inputGroup.appendChild(field);

            // Make sure the label is properly associated with the field
            if (label) {
                label.setAttribute('for', field.id);
            }
        }
        // Regular input fields
        else {
            // Create a new input group
            inputGroup = document.createElement('div');
            inputGroup.className = 'input-group';

            // Replace the field with the input group containing the field
            field.parentNode.insertBefore(inputGroup, field);
            inputGroup.appendChild(field);
        }

        // Add the toggle button to the input group
        inputGroup.appendChild(toggleButton);

        // Add click event to toggle password visibility
        toggleButton.addEventListener('click', function() {
            // Toggle the password field type
            if (field.type === 'password') {
                field.type = 'text';
                toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
                toggleButton.title = 'Hide password';
            } else {
                field.type = 'password';
                toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
                toggleButton.title = 'Show password';
            }
        });
    });

    // Add some CSS for the toggle button
    const style = document.createElement('style');
    style.textContent = `
        .password-toggle {
            cursor: pointer;
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }

        .form-floating .input-group {
            height: 100%;
        }

        .form-floating > .input-group > .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        /* Fix for floating labels */
        .form-floating > .input-group {
            display: flex;
            align-items: stretch;
            width: 100%;
        }

        .form-floating > .input-group > .form-control {
            height: calc(3.5rem + 2px);
            line-height: 1.25;
        }

        .form-floating > .input-group > .password-toggle {
            display: flex;
            align-items: center;
        }

        /* Fix for duplicate labels */
        .form-floating > .input-group label {
            display: none;
        }
    `;
    document.head.appendChild(style);
});
