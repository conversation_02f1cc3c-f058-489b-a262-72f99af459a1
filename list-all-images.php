<?php
// List all images in the entire project

function listFilesRecursively($dir, $baseDir = '') {
    if (empty($baseDir)) {
        $baseDir = $dir;
    }
    
    $results = [];
    $files = scandir($dir);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $path = $dir . '/' . $file;
        $relativePath = substr($path, strlen($baseDir) + 1);
        
        if (is_dir($path)) {
            $results = array_merge($results, listFilesRecursively($path, $baseDir));
        } else {
            // Check if it's an image file
            $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                $results[] = $relativePath;
            }
        }
    }
    
    return $results;
}

echo "<h1>All Images in Project</h1>";

$baseDir = __DIR__;
$images = listFilesRecursively($baseDir);

echo "<p>Found " . count($images) . " images</p>";

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Path</th><th>Size</th><th>Preview</th></tr>";

foreach ($images as $image) {
    $fullPath = $baseDir . '/' . $image;
    $size = filesize($fullPath);
    $webPath = str_replace('\\', '/', $image);
    
    echo "<tr>";
    echo "<td>" . $image . "</td>";
    echo "<td>" . $size . " bytes</td>";
    echo "<td><img src='" . $webPath . "' style='max-width: 100px; max-height: 100px;'></td>";
    echo "</tr>";
}

echo "</table>";
?>
