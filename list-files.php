<?php
// List all files in the current directory and subdirectories
function listFiles($dir) {
    $files = scandir($dir);

    foreach ($files as $file) {
        if ($file == '.' || $file == '..') continue;

        $path = $dir . '/' . $file;

        if (is_dir($path)) {
            echo "<strong>Directory: $path</strong><br>";
            listFiles($path);
        } else {
            echo "File: $path<br>";
        }
    }
}

// Search for specific files
function searchFiles($patterns, $dirs = ['.']) {
    echo "<h2>Search Results for Specific Files</h2>";

    $found = false;

    foreach ($dirs as $dir) {
        foreach ($patterns as $pattern) {
            $files = glob("$dir/$pattern");

            if (!empty($files)) {
                $found = true;
                echo "<h3>Files matching '$pattern' in directory '$dir':</h3>";
                echo "<ul>";
                foreach ($files as $file) {
                    echo "<li>$file</li>";
                }
                echo "</ul>";
            }
        }
    }

    if (!$found) {
        echo "<p>No files found matching the specified patterns.</p>";
    }
}

echo "<h1>Files in INVENTORY Directory</h1>";

// Search for the specific files mentioned
$patterns = [
    'check-database*',
    'check-image*',
    'copy-image*',
    'debug-image*',
    'image-upload-test*',
    'generate-placeholder*',
    'db_setup*',
    'griffin_inventory*'
];

$dirs = [
    '.',
    './database',
    './admin',
    './includes',
    './storekeeper',
    './images'
];

searchFiles($patterns, $dirs);

// Check database directory specifically
echo "<h2>Database Directory Contents</h2>";
if (is_dir('./database')) {
    $files = scandir('./database');
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo "<li>./database/$file</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p>Database directory not found.</p>";
}

// List all SQL files in the project
echo "<h2>All SQL Files</h2>";
$sqlFiles = [];
function findSqlFiles($dir, &$sqlFiles) {
    $files = scandir($dir);

    foreach ($files as $file) {
        if ($file == '.' || $file == '..') continue;

        $path = $dir . '/' . $file;

        if (is_dir($path)) {
            findSqlFiles($path, $sqlFiles);
        } else if (pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
            $sqlFiles[] = $path;
        }
    }
}

findSqlFiles('.', $sqlFiles);

if (!empty($sqlFiles)) {
    echo "<ul>";
    foreach ($sqlFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No SQL files found.</p>";
}

// List all files
echo "<h2>All Files</h2>";
listFiles('.');
?>
