<?php
// Start session
session_start();

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    // Redirect based on role
    if ($_SESSION['role'] === 'admin') {
        header("Location: admin/dashboard.php");
    } else {
        header("Location: storekeeper/dashboard.php");
    }
    exit;
}

// Include database connection
require_once 'includes/db_connect.php';

// Initialize variables
$username = $password = "";
$username_err = $password_err = $login_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate username
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter username.";
    } else {
        $username = trim($_POST["username"]);
    }

    // Validate password
    if (empty(trim($_POST["password"]))) {
        $password_err = "Please enter your password.";
    } else {
        $password = trim($_POST["password"]);
    }

    // Validate credentials
    if (empty($username_err) && empty($password_err)) {
        // Prepare a select statement
        $sql = "SELECT user_id, username, password, full_name, email, role, status, password_reset_requested FROM users WHERE username = ?";

        if ($stmt = $pdo->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bindParam(1, $param_username, PDO::PARAM_STR);

            // Set parameters
            $param_username = $username;

            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                // Check if username exists, if yes then verify password
                if ($stmt->rowCount() == 1) {
                    if ($row = $stmt->fetch()) {
                        $id = $row["user_id"];
                        $username = $row["username"];
                        $hashed_password = $row["password"];
                        $full_name = $row["full_name"];
                        $email = $row["email"];
                        $role = $row["role"];
                        $status = $row["status"];
                        $password_reset_requested = $row["password_reset_requested"];

                        // Check account status
                        if ($status === "pending") {
                            $login_err = "Your account is pending approval by an administrator.";
                        } elseif ($status === "inactive") {
                            $login_err = "Your account has been deactivated. Please contact an administrator.";
                        } elseif (password_verify($password, $hashed_password)) {
                            // Password is correct, start a new session
                            session_start();

                            // Store data in session variables
                            $_SESSION["user_id"] = $id;
                            $_SESSION["username"] = $username;
                            $_SESSION["full_name"] = $full_name;
                            $_SESSION["email"] = $email;
                            $_SESSION["role"] = $role;

                            // Update last login time
                            $update_sql = "UPDATE users SET last_login = NOW() WHERE user_id = ?";
                            $update_stmt = $pdo->prepare($update_sql);
                            $update_stmt->execute([$id]);

                            // Redirect user to appropriate dashboard
                            if ($role === "admin") {
                                header("location: admin/dashboard.php");
                            } else {
                                header("location: storekeeper/dashboard.php");
                            }
                        } else {
                            // Password is not valid
                            $login_err = "Invalid username or password.";
                        }
                    }
                } else {
                    // Username doesn't exist
                    $login_err = "Invalid username or password.";
                }
            } else {
                $login_err = "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            unset($stmt);
        }
    }

    // Close connection
    unset($pdo);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Griffin Gadgets Inventory</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            background-color: #f0f5fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }

        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 20px;
        }

        .login-card {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .login-header {
            background-color: #2c3e50;
            color: white;
            padding: 30px 20px 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .login-logo {
            margin-bottom: 15px;
            display: flex;
            justify-content: center;
        }

        .login-logo img {
            height: 70px;
            width: auto;
        }

        .login-body {
            padding: 30px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .login-footer {
            text-align: center;
            margin-top: 20px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <img src="images/logo.jpg" alt="Griffin Gadgets Logo">
                </div>
                <h2>Login</h2>
            </div>

            <div class="login-body">
                <?php if (!empty($login_err)): ?>
                    <div class="alert alert-danger"><?php echo $login_err; ?></div>
                <?php endif; ?>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <div class="form-floating">
                        <input type="text" name="username" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" id="username" placeholder="Username" value="<?php echo $username; ?>">
                        <label for="username">Username</label>
                        <div class="invalid-feedback"><?php echo $username_err; ?></div>
                    </div>

                    <div class="form-floating">
                        <input type="password" name="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" id="password" placeholder="Password">
                        <label for="password">Password</label>
                        <div class="invalid-feedback"><?php echo $password_err; ?></div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">Login</button>
                    </div>
                </form>
            </div>

            <div class="login-footer">
                <p class="mt-3">Don't have an account? <a href="register.php">Register as Storekeeper</a></p>
                <p class="mt-2">Forgot your password? <a href="reset-password.php">Request a password reset</a></p>
            </div>
        </div>

        <div class="text-center mt-3">
            <a href="index.php" class="text-decoration-none">
                <i class="fas fa-arrow-left me-1"></i> Back to Home
            </a>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Password Toggle Script -->
    <script src="js/password-toggle-fixed.js"></script>
</body>
</html>
