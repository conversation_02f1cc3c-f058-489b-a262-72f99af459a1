<?php
// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Include database connection
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Initialize variables
$current_password = $new_password = $confirm_password = "";
$current_password_err = $new_password_err = $confirm_password_err = "";
$success_message = $error_message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Check if it's a password change or password reset request
    if (isset($_POST["request_reset"]) && $_SESSION['role'] === 'storekeeper') {
        // This is a password reset request
        $stmt = $pdo->prepare("UPDATE users SET password_reset_requested = TRUE WHERE user_id = ?");

        if ($stmt->execute([$_SESSION['user_id']])) {
            $success_message = "Password reset request submitted successfully. An administrator will reset your password.";
        } else {
            $error_message = "Oops! Something went wrong. Please try again later.";
        }
    } else {
        // This is a regular password change
        // Validate current password
        if (empty(trim($_POST["current_password"]))) {
            $current_password_err = "Please enter your current password.";
        } else {
            $current_password = trim($_POST["current_password"]);

            // Check if current password is correct
            $stmt = $pdo->prepare("SELECT password FROM users WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $row = $stmt->fetch();

            if (!password_verify($current_password, $row['password'])) {
                $current_password_err = "Current password is incorrect.";
            }
        }

        // Validate new password
        if (empty(trim($_POST["new_password"]))) {
            $new_password_err = "Please enter a new password.";
        } elseif (strlen(trim($_POST["new_password"])) < 8) {
            $new_password_err = "Password must have at least 8 characters.";
        } else {
            $new_password = trim($_POST["new_password"]);
        }

        // Validate confirm password
        if (empty(trim($_POST["confirm_password"]))) {
            $confirm_password_err = "Please confirm the password.";
        } else {
            $confirm_password = trim($_POST["confirm_password"]);
            if (empty($new_password_err) && ($new_password != $confirm_password)) {
                $confirm_password_err = "Passwords did not match.";
            }
        }

        // Check input errors before updating the database
        if (empty($current_password_err) && empty($new_password_err) && empty($confirm_password_err)) {
            // Prepare an update statement
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE user_id = ?");

            // Set parameters
            $param_password = password_hash($new_password, PASSWORD_DEFAULT);
            $param_id = $_SESSION['user_id'];

            // Attempt to execute the prepared statement
            if ($stmt->execute([$param_password, $param_id])) {
                // Password updated successfully
                $success_message = "Password changed successfully.";

                // Clear form data
                $current_password = $new_password = $confirm_password = "";
            } else {
                $error_message = "Oops! Something went wrong. Please try again later.";
            }
        }
    }
}

// Get user data
$stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

// Check if password reset is already requested
$password_reset_requested = $user['password_reset_requested'] ?? false;

// Get user's recent activity
$stmt = $pdo->prepare("
    SELECT t.*, p.product_name
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    WHERE t.user_id = ?
    ORDER BY t.transaction_date DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$recent_activity = $stmt->fetchAll();

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">My Profile</h1>
    <a href="<?php echo isAdmin() ? 'admin/dashboard.php' : 'storekeeper/dashboard.php'; ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
    </a>
</div>

<?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="row g-4">
    <div class="col-lg-4">
        <!-- Profile Card -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body text-center">
                <div class="mb-3">
                    <div class="avatar-circle mx-auto">
                        <span class="avatar-text"><?php echo substr($user['full_name'], 0, 1); ?></span>
                    </div>
                </div>
                <h5 class="card-title"><?php echo htmlspecialchars($user['full_name']); ?></h5>
                <p class="text-muted mb-1"><?php echo ucfirst($user['role']); ?></p>
                <p class="text-muted mb-3"><?php echo htmlspecialchars($user['email']); ?></p>

                <div class="d-flex justify-content-center">
                    <div class="px-3 border-end">
                        <h6 class="mb-0">Username</h6>
                        <p class="text-muted mb-0"><?php echo htmlspecialchars($user['username']); ?></p>
                    </div>
                    <div class="px-3">
                        <h6 class="mb-0">Last Login</h6>
                        <p class="text-muted mb-0"><?php echo $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'N/A'; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change Password Card -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Change Password</h5>
            </div>
            <div class="card-body">
                <?php if ($_SESSION['role'] === 'storekeeper' && $password_reset_requested): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> Your password reset request has been submitted and is pending approval by an administrator.
                    </div>
                <?php elseif ($_SESSION['role'] === 'storekeeper'): ?>
                    <div class="d-flex justify-content-between mb-3">
                        <button type="button" id="changePasswordBtn" class="btn btn-primary">Change Password</button>
                        <button type="button" id="requestResetBtn" class="btn btn-outline-secondary">Request Admin Reset</button>
                    </div>

                    <form id="passwordChangeForm" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" name="current_password" id="current_password" class="form-control <?php echo (!empty($current_password_err)) ? 'is-invalid' : ''; ?>" required>
                            <div class="invalid-feedback">
                                <?php echo $current_password_err; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" name="new_password" id="new_password" class="form-control <?php echo (!empty($new_password_err)) ? 'is-invalid' : ''; ?>" required>
                            <div class="invalid-feedback">
                                <?php echo $new_password_err; ?>
                            </div>
                            <div class="form-text">Password must be at least 8 characters long.</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" name="confirm_password" id="confirm_password" class="form-control <?php echo (!empty($confirm_password_err)) ? 'is-invalid' : ''; ?>" required>
                            <div class="invalid-feedback">
                                <?php echo $confirm_password_err; ?>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>

                    <form id="passwordResetForm" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" style="display: none;">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> If you've forgotten your password, you can request an administrator to reset it for you.
                        </div>
                        <input type="hidden" name="request_reset" value="1">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning">Request Password Reset</button>
                        </div>
                    </form>
                <?php else: ?>
                    <!-- Admin password change form -->
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" name="current_password" id="current_password" class="form-control <?php echo (!empty($current_password_err)) ? 'is-invalid' : ''; ?>" required>
                            <div class="invalid-feedback">
                                <?php echo $current_password_err; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" name="new_password" id="new_password" class="form-control <?php echo (!empty($new_password_err)) ? 'is-invalid' : ''; ?>" required>
                            <div class="invalid-feedback">
                                <?php echo $new_password_err; ?>
                            </div>
                            <div class="form-text">Password must be at least 8 characters long.</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" name="confirm_password" id="confirm_password" class="form-control <?php echo (!empty($confirm_password_err)) ? 'is-invalid' : ''; ?>" required>
                            <div class="invalid-feedback">
                                <?php echo $confirm_password_err; ?>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <!-- Recent Activity Card -->
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">Recent Activity</h5>
            </div>
            <div class="card-body">
                <?php if (count($recent_activity) > 0): ?>
                    <div class="timeline">
                        <?php foreach ($recent_activity as $activity): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker <?php echo $activity['transaction_type'] == 'in' ? 'bg-success' : 'bg-danger'; ?>">
                                    <i class="fas <?php echo $activity['transaction_type'] == 'in' ? 'fa-plus' : 'fa-minus'; ?>"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-1">
                                            <?php echo $activity['transaction_type'] == 'in' ? 'Added Stock' : 'Recorded Sale'; ?>:
                                            <?php echo htmlspecialchars($activity['product_name']); ?>
                                        </h6>
                                        <span class="text-muted small"><?php echo date('M d, Y H:i', strtotime($activity['transaction_date'])); ?></span>
                                    </div>
                                    <p class="mb-0">
                                        Quantity: <?php echo $activity['quantity']; ?>
                                        <?php if (!empty($activity['notes'])): ?>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($activity['notes']); ?></small>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <p class="mb-0">No recent activity found.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-text {
    font-size: 48px;
    color: white;
    font-weight: bold;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline-content {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.timeline:before {
    content: '';
    position: absolute;
    left: -20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

/* Password toggle button styles */
.password-toggle {
    cursor: pointer;
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group .invalid-feedback {
    position: absolute;
    bottom: -20px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        }, false);
    });

    // Toggle between password change and reset request forms for storekeepers
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const requestResetBtn = document.getElementById('requestResetBtn');
    const passwordChangeForm = document.getElementById('passwordChangeForm');
    const passwordResetForm = document.getElementById('passwordResetForm');

    if (changePasswordBtn && requestResetBtn) {
        changePasswordBtn.addEventListener('click', function() {
            passwordChangeForm.style.display = 'block';
            passwordResetForm.style.display = 'none';
            changePasswordBtn.classList.add('btn-primary');
            changePasswordBtn.classList.remove('btn-outline-primary');
            requestResetBtn.classList.add('btn-outline-secondary');
            requestResetBtn.classList.remove('btn-secondary');
        });

        requestResetBtn.addEventListener('click', function() {
            passwordChangeForm.style.display = 'none';
            passwordResetForm.style.display = 'block';
            changePasswordBtn.classList.add('btn-outline-primary');
            changePasswordBtn.classList.remove('btn-primary');
            requestResetBtn.classList.add('btn-secondary');
            requestResetBtn.classList.remove('btn-outline-secondary');
        });
    }

    // Load the password toggle script
    const script = document.createElement('script');
    script.src = '<?php echo isset($is_admin) || isset($is_storekeeper) ? '../' : ''; ?>js/password-toggle-fixed.js';
    document.head.appendChild(script);
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
