=======================================================================
                GRIFFIN GADGETS INVENTORY SYSTEM
                    PROGRAM DOCUMENTATION
=======================================================================

TABLE OF CONTENTS
----------------
1. System Overview
2. Features and Functionality
3. User Roles and Permissions
4. Database Structure
5. System Architecture
6. Security Measures
7. Maintenance and Support

-----------------------------------------------------------------------
1. SYSTEM OVERVIEW
-----------------------------------------------------------------------

The Griffin Gadgets Inventory System is a comprehensive web-based application 
designed to manage inventory for a retail electronics store specializing in 
phones (iPhones and Samsung) and gaming products. The system provides real-time 
tracking of product quantities, sales recording, stock management, and 
reporting capabilities.

The system is built using PHP, MySQL, HTML, CSS, JavaScript, and Bootstrap, 
making it both powerful and user-friendly. It features a responsive design 
that works across desktop and mobile devices.

-----------------------------------------------------------------------
2. FEATURES AND FUNCTIONALITY
-----------------------------------------------------------------------

2.1 Dashboard
    - Real-time overview of inventory status
    - Quick access to key metrics (total products, stock value, etc.)
    - Visual indicators for low stock and out-of-stock items
    - Recent transaction history

2.2 Product Management
    - Add, edit, and delete products
    - Set alert levels for low stock notifications
    - Upload and manage product images
    - Categorize products by type

2.3 Inventory Control
    - Record stock additions (purchases)
    - Record stock removals (sales)
    - Track inventory levels in real-time
    - Receive alerts for low stock items

2.4 User Management
    - Create and manage user accounts
    - Assign roles (admin, storekeeper)
    - Approve new storekeeper registrations
    - Reset passwords

2.5 Reporting
    - Stock value reports
    - Sales history reports
    - Low stock alerts
    - Transaction logs

2.6 Security
    - Secure login system
    - Role-based access control
    - Password encryption
    - Session management

-----------------------------------------------------------------------
3. USER ROLES AND PERMISSIONS
-----------------------------------------------------------------------

3.1 Administrator
    - Full access to all system features
    - Manage users (create, edit, delete, approve)
    - Manage products (add, edit, delete)
    - View all reports and dashboards
    - Reset user passwords
    - Configure system settings

3.2 Storekeeper
    - Limited access based on assigned permissions
    - Record sales and stock additions
    - View inventory levels
    - View low stock alerts
    - Cannot access user management
    - Cannot delete products or transactions

-----------------------------------------------------------------------
4. DATABASE STRUCTURE
-----------------------------------------------------------------------

4.1 Users Table
    - user_id: Unique identifier for each user
    - username: Login username
    - password: Encrypted password
    - full_name: User's full name
    - email: User's email address
    - role: User role (admin or storekeeper)
    - status: Account status (active, inactive, pending)
    - password_reset_requested: Flag for password reset requests
    - created_at: Account creation timestamp
    - last_login: Last login timestamp

4.2 Products Table
    - product_id: Unique identifier for each product
    - product_name: Name of the product
    - description: Product description
    - quantity: Current stock quantity
    - price: Product price in Nigerian Naira
    - alert_level: Threshold for low stock alerts
    - created_at: Product creation timestamp
    - updated_at: Last update timestamp

4.3 Stock Transactions Table
    - transaction_id: Unique identifier for each transaction
    - product_id: Reference to the product
    - user_id: Reference to the user who performed the transaction
    - quantity: Transaction quantity
    - transaction_type: Type of transaction (in or out)
    - notes: Additional transaction notes
    - transaction_date: Transaction timestamp

-----------------------------------------------------------------------
5. SYSTEM ARCHITECTURE
-----------------------------------------------------------------------

5.1 Front-End
    - HTML5 for structure
    - CSS3 for styling
    - Bootstrap 5 for responsive design
    - JavaScript for client-side functionality
    - Font Awesome for icons

5.2 Back-End
    - PHP 8.x for server-side processing
    - MySQL for database management
    - PDO for database connections
    - Session management for user authentication

5.3 Directory Structure
    - /admin: Administrator interface files
    - /storekeeper: Storekeeper interface files
    - /includes: Shared PHP functions and components
    - /images: System images and product photos
    - /database: Database schema and import files
    - /css: Custom CSS styles
    - /js: JavaScript files

-----------------------------------------------------------------------
6. SECURITY MEASURES
-----------------------------------------------------------------------

6.1 Authentication
    - Secure password hashing using PHP's password_hash()
    - Session-based authentication
    - CSRF protection for forms
    - Input validation and sanitization

6.2 Authorization
    - Role-based access control
    - Page-level permission checks
    - Function-level permission checks

6.3 Data Protection
    - Prepared statements to prevent SQL injection
    - Input sanitization to prevent XSS attacks
    - Output escaping for displayed data
    - Error handling and logging

-----------------------------------------------------------------------
7. MAINTENANCE AND SUPPORT
-----------------------------------------------------------------------

7.1 Regular Maintenance
    - Database backups
    - System updates
    - Security patches
    - Performance optimization

7.2 Troubleshooting
    - Check server logs for errors
    - Verify database connection
    - Ensure proper file permissions
    - Test in different browsers

7.3 Support Contact
    - Griffin Gadgets
    - 37a St. Michaels Aba
    - +234-810-079-5854
    - <EMAIL>

=======================================================================
                        END OF DOCUMENT
=======================================================================
