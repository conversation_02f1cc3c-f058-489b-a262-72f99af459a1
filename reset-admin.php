<?php
// Include database connection
require_once 'includes/db_connect.php';

// Initialize variables
$success = false;
$error = false;
$message = '';

// Process reset request
if (isset($_POST['reset'])) {
    try {
        // Start transaction
        $pdo->beginTransaction();

        // First, check if there are any non-admin users we can reassign transactions to
        $stmt = $pdo->query("SELECT user_id FROM users WHERE role = 'storekeeper' LIMIT 1");
        $storekeeper = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($storekeeper) {
            // Get admin user IDs
            $stmt = $pdo->query("SELECT user_id FROM users WHERE role = 'admin'");
            $adminUsers = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (!empty($adminUsers)) {
                // Reassign transactions from admin users to the storekeeper
                $placeholders = implode(',', array_fill(0, count($adminUsers), '?'));
                $updateStmt = $pdo->prepare("UPDATE stock_transactions SET user_id = ? WHERE user_id IN ($placeholders)");

                $params = [$storekeeper['user_id']];
                foreach ($adminUsers as $adminId) {
                    $params[] = $adminId;
                }

                $updateStmt->execute($params);

                // Now delete admin users
                $stmt = $pdo->prepare("DELETE FROM users WHERE role = 'admin'");
                $stmt->execute();

                $pdo->commit();
                $success = true;
                $message = "All admin accounts have been removed. You can now create a new admin account.";
            } else {
                $pdo->commit();
                $success = true;
                $message = "No admin accounts found to remove.";
            }
        } else {
            // Create a temporary user to reassign transactions
            $tempUsername = "temp_user_" . time();
            $tempPassword = password_hash("temporary", PASSWORD_DEFAULT);

            $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role, status)
                                  VALUES (?, ?, 'Temporary User', '<EMAIL>', 'storekeeper', 'inactive')");
            $stmt->execute([$tempUsername, $tempPassword]);
            $tempUserId = $pdo->lastInsertId();

            // Get admin user IDs
            $stmt = $pdo->query("SELECT user_id FROM users WHERE role = 'admin'");
            $adminUsers = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (!empty($adminUsers)) {
                // Reassign transactions from admin users to the temporary user
                $placeholders = implode(',', array_fill(0, count($adminUsers), '?'));
                $updateStmt = $pdo->prepare("UPDATE stock_transactions SET user_id = ? WHERE user_id IN ($placeholders)");

                $params = [$tempUserId];
                foreach ($adminUsers as $adminId) {
                    $params[] = $adminId;
                }

                $updateStmt->execute($params);

                // Now delete admin users
                $stmt = $pdo->prepare("DELETE FROM users WHERE role = 'admin'");
                $stmt->execute();

                $pdo->commit();
                $success = true;
                $message = "All admin accounts have been removed. You can now create a new admin account.";
            } else {
                $pdo->commit();
                $success = true;
                $message = "No admin accounts found to remove.";
            }
        }
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = true;
        $message = "Database error: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Admin - Griffin Gadgets Inventory</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            background-color: #f0f5fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background-color: #2c3e50;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand img {
            height: 40px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .main-content {
            flex: 1;
            padding: 2rem 0;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            font-weight: 600;
            padding: 1.25rem;
        }

        .footer {
            background-color: #2c3e50;
            color: white;
            padding: 1.5rem 0;
            margin-top: auto;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#">
                <img src="images/logo.jpg" alt="Griffin Gadgets Logo" class="me-2">
                <span>Griffin Gadgets</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <?php if ($success): ?>
                        <div class="card mb-4">
                            <div class="card-body text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-check-circle text-success fa-5x"></i>
                                </div>
                                <h2 class="mb-3">Admin Reset Complete</h2>
                                <p class="lead mb-4"><?php echo $message; ?></p>
                                <div class="d-grid gap-2 col-md-8 mx-auto">
                                    <a href="adminsetup.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-user-plus me-2"></i>Create New Admin Account
                                    </a>
                                    <a href="index.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-home me-2"></i>Return to Home
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php elseif ($error): ?>
                        <div class="card mb-4">
                            <div class="card-body text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-exclamation-circle text-danger fa-5x"></i>
                                </div>
                                <h2 class="mb-3">Error</h2>
                                <p class="lead mb-4"><?php echo $message; ?></p>
                                <div class="d-grid gap-2 col-md-8 mx-auto">
                                    <a href="reset-admin.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-redo me-2"></i>Try Again
                                    </a>
                                    <a href="index.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-home me-2"></i>Return to Home
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4 class="mb-0"><i class="fas fa-user-shield me-2"></i>Reset Admin Account</h4>
                            </div>
                            <div class="card-body p-4">
                                <div class="text-center mb-4">
                                    <img src="images/logo.jpg" alt="Griffin Gadgets Logo" style="height: 80px; border-radius: 50%; border: 2px solid white;" class="mb-3">
                                    <h2>Griffin Gadgets Inventory System</h2>
                                    <p class="text-muted">Remove existing admin account</p>
                                </div>

                                <div class="alert alert-warning">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                        </div>
                                        <div>
                                            <h5 class="alert-heading">Warning!</h5>
                                            <p class="mb-0">This action will remove all existing admin accounts from the system. You will need to create a new admin account after this process. This action cannot be undone.</p>
                                        </div>
                                    </div>
                                </div>

                                <form method="post" action="">
                                    <div class="d-grid gap-2 mt-4">
                                        <button type="submit" name="reset" class="btn btn-danger btn-lg">
                                            <i class="fas fa-trash-alt me-2"></i>Remove Admin Accounts
                                        </button>
                                        <a href="index.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container text-center">
            <p class="mb-0">Griffin Gadgets | 37a St. Michaels Aba | +234-810-079-5854 | <EMAIL></p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
