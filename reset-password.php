<?php
// Include database connection
require_once 'includes/db_connect.php';

// Initialize variables
$username = $email = "";
$username_err = $email_err = "";
$success_message = $error_message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate username
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter your username.";
    } else {
        $username = trim($_POST["username"]);
    }

    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter your email.";
    } else {
        $email = trim($_POST["email"]);
    }

    // Check input errors before processing the request
    if (empty($username_err) && empty($email_err)) {
        // Prepare a select statement
        $sql = "SELECT user_id, username, email, role, status FROM users WHERE username = ? AND email = ?";

        if ($stmt = $pdo->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bindParam(1, $param_username, PDO::PARAM_STR);
            $stmt->bindParam(2, $param_email, PDO::PARAM_STR);

            // Set parameters
            $param_username = $username;
            $param_email = $email;

            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                if ($stmt->rowCount() == 1) {
                    $row = $stmt->fetch();

                    // Check if user is a storekeeper
                    if ($row["role"] !== "storekeeper") {
                        $error_message = "Password reset is only available for storekeepers. Admins should contact the system administrator.";
                    } elseif ($row["status"] === "pending") {
                        $error_message = "Your account is pending approval. Please wait for an administrator to approve your account.";
                    } elseif ($row["status"] === "inactive") {
                        $error_message = "Your account is inactive. Please contact an administrator.";
                    } else {
                        // Update password_reset_requested flag
                        $update_sql = "UPDATE users SET password_reset_requested = TRUE WHERE user_id = ?";
                        $update_stmt = $pdo->prepare($update_sql);

                        if ($update_stmt->execute([$row["user_id"]])) {
                            $success_message = "Password reset request submitted successfully. An administrator will review your request and reset your password.";

                            // Clear form data
                            $username = $email = "";
                        } else {
                            $error_message = "Oops! Something went wrong. Please try again later.";
                        }
                    }
                } else {
                    $error_message = "No account found with that username and email.";
                }
            } else {
                $error_message = "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            unset($stmt);
        }
    }

    // Close connection
    unset($pdo);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Griffin Gadgets Inventory</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            background-color: #f0f5fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }

        .reset-container {
            max-width: 500px;
            width: 100%;
            padding: 20px;
        }

        .reset-card {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .reset-header {
            background-color: #2c3e50;
            color: white;
            padding: 30px 20px 20px;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .reset-logo {
            margin-bottom: 15px;
            display: flex;
            justify-content: center;
        }

        .reset-logo img {
            height: 70px;
            width: auto;
        }

        .reset-body {
            padding: 30px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .password-note {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 3px solid #17a2b8;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <div class="reset-logo">
                    <img src="images/logo.jpg" alt="Griffin Gadgets Logo">
                </div>
                <h2>Password Reset Request</h2>
            </div>

            <div class="reset-body">
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success">
                        <?php echo $success_message; ?>
                        <hr>
                        <p>Your default password will be <strong>12345678</strong> after the reset is approved.</p>
                        <p class="mb-0">You can <a href="login.php">login</a> once your password has been reset.</p>
                    </div>
                <?php elseif (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <p class="text-muted mb-4">Please enter your username and email address. An administrator will review your request and reset your password.</p>
                <div class="password-note">
                    <i class="fas fa-info-circle me-2"></i> When your password is reset, the default password will be <strong>12345678</strong>. You should change it after logging in.
                </div>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <div class="form-floating mb-3">
                        <input type="text" name="username" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" id="username" placeholder="Username" value="<?php echo $username; ?>">
                        <label for="username">Username</label>
                        <div class="invalid-feedback"><?php echo $username_err; ?></div>
                    </div>

                    <div class="form-floating mb-4">
                        <input type="email" name="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" id="email" placeholder="Email" value="<?php echo $email; ?>">
                        <label for="email">Email</label>
                        <div class="invalid-feedback"><?php echo $email_err; ?></div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">Submit Request</button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <a href="login.php" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i> Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Password Toggle Script -->
    <script src="js/password-toggle-fixed.js"></script>
</body>
</html>
