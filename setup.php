<?php
// Database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Create connection to MySQL server
    $pdo = new PDO("mysql:host=$host", $username, $password);

    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS griffin_inventory");

    echo "Database created successfully<br>";

    // Select the database
    $pdo->exec("USE griffin_inventory");

    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            role ENUM('admin', 'storekeeper') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL
        )
    ");

    echo "Users table created successfully<br>";

    // Create products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            product_id INT AUTO_INCREMENT PRIMARY KEY,
            product_name VARCHAR(100) NOT NULL,
            category VARCHAR(50) NOT NULL,
            description TEXT,
            price DECIMAL(10, 2) NOT NULL,
            quantity INT NOT NULL DEFAULT 0,
            alert_level INT NOT NULL DEFAULT 5,
            image_path VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    echo "Products table created successfully<br>";

    // Create stock_transactions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS stock_transactions (
            transaction_id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            user_id INT NOT NULL,
            transaction_type ENUM('in', 'out') NOT NULL,
            quantity INT NOT NULL,
            transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (product_id) REFERENCES products(product_id),
            FOREIGN KEY (user_id) REFERENCES users(user_id)
        )
    ");

    echo "Stock transactions table created successfully<br>";

    // Modify users table to add status and password reset fields
    $pdo->exec("
        ALTER TABLE users
        ADD COLUMN IF NOT EXISTS status ENUM('active', 'pending', 'inactive') DEFAULT 'active',
        ADD COLUMN IF NOT EXISTS password_reset_requested BOOLEAN DEFAULT FALSE
    ");

    echo "Users table updated successfully<br>";

    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin_exists = (int)$stmt->fetchColumn();

    if (!$admin_exists) {
        // Insert default admin user (password: admin@2023)
        $admin_password = password_hash('admin@2023', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $admin_password, 'System Administrator', '<EMAIL>', 'admin', 'active']);
        echo "Admin user created successfully<br>";
    } else {
        echo "Admin user already exists<br>";
    }

    // We won't create a default storekeeper user since they need to register and be approved

    // Check if sample products exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products");
    $stmt->execute();
    $products_count = (int)$stmt->fetchColumn();

    if ($products_count == 0) {
        // Insert sample products with prices in Naira
        $sample_products = [
            ['iPhone 13 Pro', 'Phones', '6.1-inch Super Retina XDR display with ProMotion, A15 Bionic chip', 750000, 15, 5, 'images/products/13pro.jpg'],
            ['iPhone 14', 'Phones', 'Latest iPhone with improved camera and performance', 650000, 20, 8, 'images/products/14pro.jpg'],
            ['Samsung Galaxy S22', 'Phones', 'Dynamic AMOLED 2X display, 120Hz refresh rate', 550000, 18, 6, 'images/products/s22.jpg'],
            ['Samsung Galaxy Z Fold 4', 'Phones', 'Foldable smartphone with large display', 1200000, 8, 3, 'images/products/zfold4.jpg'],
            ['PlayStation 5', 'Gaming', 'Next-gen gaming console with ultra-high-speed SSD', 450000, 10, 4, 'images/products/playstation5.jpg']
        ];

        $stmt = $pdo->prepare("
            INSERT INTO products (product_name, category, description, price, quantity, alert_level, image_path)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($sample_products as $product) {
            $stmt->execute($product);
        }

        echo "Sample products created successfully<br>";

        // Get admin user ID
        $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = 'admin'");
        $stmt->execute();
        $admin_id = $stmt->fetchColumn();

        // Insert initial stock transactions
        $stmt = $pdo->prepare("
            INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes)
            VALUES (?, ?, 'in', ?, 'Initial stock')
        ");

        // Get all products
        $products_stmt = $pdo->query("SELECT product_id, quantity FROM products");
        $products = $products_stmt->fetchAll();

        foreach ($products as $product) {
            $stmt->execute([$product['product_id'], $admin_id, $product['quantity']]);
        }

        echo "Initial stock transactions created successfully<br>";
    } else {
        echo "Products already exist<br>";
    }

    echo "<br>Setup completed successfully! <a href='index.php'>Go to homepage</a>";

} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
