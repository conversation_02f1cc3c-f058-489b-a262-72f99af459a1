<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Get low stock products
$stmt = $pdo->query("SELECT * FROM products WHERE quantity <= alert_level ORDER BY (quantity = 0) DESC, (quantity / alert_level) ASC");
$low_stock_products = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Stock Alerts</h1>
    <a href="dashboard.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
    </a>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Low Stock Items</h5>
            <span class="badge bg-warning text-dark"><?php echo count($low_stock_products); ?> items</span>
        </div>
    </div>
    <div class="card-body">
        <?php if (count($low_stock_products) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th>Current Stock</th>
                            <th>Alert Level</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($low_stock_products as $product): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo !empty($product['image_path']) ? '../' . $product['image_path'] : 'https://via.placeholder.com/40'; ?>"
                                             alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                                             class="product-image me-2">
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($product['product_name']); ?></div>
                                            <div class="small text-muted"><?php echo formatCurrency($product['price']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($product['category']); ?></td>
                                <td>
                                    <span class="fw-bold <?php echo $product['quantity'] == 0 ? 'text-danger' : 'text-warning'; ?>">
                                        <?php echo $product['quantity']; ?>
                                    </span>
                                </td>
                                <td><?php echo $product['alert_level']; ?></td>
                                <td><?php echo getStockStatusBadge($product['quantity'], $product['alert_level']); ?></td>
                                <td>
                                    <?php if ($product['quantity'] > 0): ?>
                                        <a href="record-sale.php?product_id=<?php echo $product['product_id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-shopping-cart"></i> Record Sale
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-outline-secondary" disabled>
                                            <i class="fas fa-times-circle"></i> Out of Stock
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i> Please notify the administrator about low stock items.
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-check-circle text-success fa-4x mb-3"></i>
                <h5>All Good!</h5>
                <p class="text-muted">No low stock alerts at the moment.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
include '../includes/footer.php';
?>
