<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Get total products count
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
$total_products = $stmt->fetch()['total'];

// Get available products count (quantity > 0)
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE quantity > 0");
$available_products = $stmt->fetch()['total'];

// Get low stock products count
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE quantity <= alert_level AND quantity > 0");
$low_stock_count = $stmt->fetch()['total'];

// Get out of stock products count
$stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE quantity = 0");
$out_of_stock_count = $stmt->fetch()['total'];

// Get today's sales
$today = date('Y-m-d');
$stmt = $pdo->prepare("
    SELECT COUNT(t.transaction_id) as transaction_count, SUM(t.quantity) as total_quantity, SUM(t.quantity * p.price) as total_sales
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    WHERE t.transaction_type = 'out' AND DATE(t.transaction_date) = ?
");
$stmt->execute([$today]);
$today_sales = $stmt->fetch();
$today_quantity = $today_sales['total_quantity'] ?: 0;
$today_value = $today_sales['total_sales'] ?: 0;
$today_transaction_count = $today_sales['transaction_count'] ?: 0;

// Get recent transactions by this user
$stmt = $pdo->prepare("
    SELECT t.*, p.product_name, p.price
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    WHERE t.user_id = ? AND t.transaction_type = 'out'
    ORDER BY t.transaction_date DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$recent_transactions = $stmt->fetchAll();

// Get low stock products
$low_stock_products = getLowStockProducts($pdo);

// Include header
include '../includes/header.php';
?>

<style>
    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .stat-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
</style>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Storekeeper Dashboard</h1>
        <p class="text-muted">Welcome back, <?php echo $_SESSION['full_name']; ?></p>
    </div>
    <div class="col-md-6 text-md-end">
        <p class="mb-0">Today: <?php echo date('F d, Y'); ?></p>
    </div>
</div>

<!-- Dashboard Stats -->
<div class="row g-4 mb-4">
    <div class="col-md-6 col-lg-3" data-animate>
        <a href="available-products.php" class="text-decoration-none">
            <div class="card h-100 stat-card card-hover" style="background-color: #3498db; color: white; border: none;">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-white-50 mb-1">Available Products</h6>
                            <h2 class="mb-0 fw-bold"><?php echo $available_products; ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3" data-animate>
        <a href="stock-book.php" class="text-decoration-none">
            <div class="card h-100 stat-card card-hover" style="background-color: #27ae60; color: white; border: none;">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-white-50 mb-1">Stock Book</h6>
                            <h2 class="mb-0 fw-bold">Report</h2>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3" data-animate>
        <a href="sales-history.php" class="text-decoration-none">
            <div class="card h-100 stat-card card-hover" style="background-color: #2ecc71; color: white; border: none;">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-white-50 mb-1">Today's Sales</h6>
                            <h2 class="mb-0 fw-bold"><?php echo $today_quantity; ?> items</h2>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3" data-animate>
        <a href="alerts.php" class="text-decoration-none">
            <div class="card h-100 stat-card card-hover" style="background-color: #f39c12; color: white; border: none;">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-white-50 mb-1">Low Stock Items</h6>
                            <h2 class="mb-0 fw-bold"><?php echo $low_stock_count; ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3" data-animate>
        <a href="out-of-stock.php" class="text-decoration-none">
            <div class="card h-100 stat-card card-hover" style="background-color: #e74c3c; color: white; border: none;">
                <div class="card-body p-4">
                    <div class="d-flex flex-column">
                        <div class="mb-2">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                        <div class="mt-2">
                            <h6 class="text-white-50 mb-1">Out of Stock</h6>
                            <h2 class="mb-0 fw-bold"><?php echo $out_of_stock_count; ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>

<div class="row g-4">
    <!-- Quick Actions -->
    <div class="col-lg-4" data-animate>
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="record-sale.php" class="btn btn-lg" style="background-color: #3498db; color: white;">
                        <i class="fas fa-shopping-cart me-2"></i> Record Sale
                    </a>
                    <a href="stock-book.php" class="btn btn-lg" style="background-color: #27ae60; color: white;">
                        <i class="fas fa-book me-2"></i> Stock Book Report
                    </a>
                    <a href="inventory.php" class="btn btn-lg" style="background-color: #2c3e50; color: white;">
                        <i class="fas fa-boxes me-2"></i> View Inventory
                    </a>
                    <a href="alerts.php" class="btn btn-lg" style="background-color: #f39c12; color: white;">
                        <i class="fas fa-exclamation-triangle me-2"></i> View Alerts
                        <?php if ($low_stock_count > 0): ?>
                            <span class="badge bg-danger ms-2"><?php echo $low_stock_count; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Sales -->
    <div class="col-lg-8" data-animate>
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-dark text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Your Recent Sales</h5>
                    <a href="sales-history.php" class="btn btn-sm" style="background-color: #3498db; color: white;">View All</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($recent_transactions) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Value</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('M d, Y H:i', strtotime($transaction['transaction_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['product_name']); ?></td>
                                        <td><?php echo $transaction['quantity']; ?></td>
                                        <td><?php echo formatCurrency($transaction['quantity'] * $transaction['price']); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['notes'] ?: '-'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart text-muted fa-3x mb-3"></i>
                        <p>No sales recorded yet.</p>
                        <a href="record-sale.php" class="btn btn-primary">Record Your First Sale</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Low Stock Alerts -->
<div class="row mt-4">
    <div class="col-12" data-animate>
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-dark text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Low Stock Alerts</h5>
                    <a href="alerts.php" class="btn btn-sm" style="background-color: #3498db; color: white;">View All</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($low_stock_products) > 0): ?>
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                        <?php foreach (array_slice($low_stock_products, 0, 3) as $product): ?>
                            <div class="col">
                                <div class="alert-container">
                                    <div class="card h-100">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <div class="alert-icon <?php echo $product['quantity'] <= 0 ? 'danger' : 'warning'; ?>">
                                                    <i class="fas <?php echo $product['quantity'] <= 0 ? 'fa-times' : 'fa-exclamation'; ?>"></i>
                                                </div>
                                                <div class="ms-3">
                                                    <h6 class="mb-0"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                                    <div class="d-flex align-items-center mt-1">
                                                        <span class="me-3">
                                                            <strong>Current:</strong> <?php echo $product['quantity']; ?>
                                                        </span>
                                                        <span>
                                                            <strong>Alert Level:</strong> <?php echo $product['alert_level']; ?>
                                                        </span>
                                                    </div>
                                                    <div class="mt-2">
                                                        <span class="badge <?php echo $product['quantity'] <= 0 ? 'bg-danger' : 'bg-warning text-dark'; ?>">
                                                            <?php echo $product['quantity'] <= 0 ? 'Out of Stock' : 'Low Stock'; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if (count($low_stock_products) > 3): ?>
                        <div class="text-center mt-3">
                            <a href="alerts.php" class="btn" style="background-color: #f39c12; color: white;">View All <?php echo count($low_stock_products); ?> Alerts</a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <p>No low stock alerts at the moment.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include '../includes/footer.php';
?>
