<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Filter parameters
$category = isset($_GET['category']) ? $_GET['category'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Build query based on filters
$query = "SELECT * FROM products";
$where_clauses = [];
$params = [];

if (!empty($search)) {
    $where_clauses[] = "(product_name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category)) {
    $where_clauses[] = "category = ?";
    $params[] = $category;
}

if (!empty($status)) {
    if ($status == 'in-stock') {
        $where_clauses[] = "quantity > alert_level";
    } elseif ($status == 'low-stock') {
        $where_clauses[] = "quantity > 0 AND quantity <= alert_level";
    } elseif ($status == 'out-of-stock') {
        $where_clauses[] = "quantity = 0";
    }
}

if (!empty($where_clauses)) {
    $query .= " WHERE " . implode(" AND ", $where_clauses);
}

$query .= " ORDER BY category, product_name";

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->query("SELECT DISTINCT category FROM products ORDER BY category");
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Inventory</h1>
    <a href="record-sale.php" class="btn btn-primary">
        <i class="fas fa-shopping-cart me-1"></i> Record Sale
    </a>
</div>

<!-- Search and Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form action="" method="get" class="row g-3">
            <div class="col-md-5">
                <div class="input-group">
                    <span class="input-group-text bg-white border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" name="search" class="form-control border-start-0" placeholder="Search products..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
            </div>
            <div class="col-md-3">
                <select name="category" class="form-select">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $cat): ?>
                        <option value="<?php echo $cat; ?>" <?php echo ($category == $cat) ? 'selected' : ''; ?>>
                            <?php echo $cat; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="in-stock" <?php echo ($status == 'in-stock') ? 'selected' : ''; ?>>In Stock</option>
                    <option value="low-stock" <?php echo ($status == 'low-stock') ? 'selected' : ''; ?>>Low Stock</option>
                    <option value="out-of-stock" <?php echo ($status == 'out-of-stock') ? 'selected' : ''; ?>>Out of Stock</option>
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Products Grid -->
<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4">
    <?php if (count($products) > 0): ?>
        <?php foreach ($products as $product): ?>
            <div class="col" data-animate>
                <div class="card product-card h-100">
                    <div style="background-color: white; padding: 10px;">
                        <img src="<?php echo htmlspecialchars(getImageUrl($product['image_path'])); ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                             style="height: 180px; object-fit: contain;">
                    </div>
                    <div class="card-body">
                        <h5 class="card-title"><?php echo htmlspecialchars($product['product_name']); ?></h5>
                        <p class="card-price"><?php echo formatCurrency($product['price']); ?></p>
                        <p class="card-text small text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 100) . (strlen($product['description']) > 100 ? '...' : '')); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <?php echo getStockStatusBadge($product['quantity'], $product['alert_level']); ?>
                            <span class="text-muted"><?php echo $product['quantity']; ?> units</span>
                        </div>
                    </div>
                    <div class="card-footer bg-white">
                        <div class="d-grid">
                            <?php if ($product['quantity'] > 0): ?>
                                <a href="record-sale.php?product_id=<?php echo $product['product_id']; ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-shopping-cart me-1"></i> Record Sale
                                </a>
                            <?php else: ?>
                                <button class="btn btn-outline-secondary" disabled>
                                    <i class="fas fa-times-circle me-1"></i> Out of Stock
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="col-12">
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i> No products found matching your criteria.
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Include footer
include '../includes/footer.php';
?>
