<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Get out of stock products (quantity = 0)
$stmt = $pdo->query("
    SELECT *
    FROM products
    WHERE quantity = 0
    ORDER BY product_name ASC
");
$out_of_stock_products = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<style>
    .product-table th {
        font-weight: 600;
        background-color: #f8f9fa;
    }

    .search-container {
        position: relative;
    }

    .search-container i {
        position: absolute;
        left: 10px;
        top: 10px;
        color: #6c757d;
    }

    #productSearch {
        padding-left: 30px;
        border-radius: 20px;
    }

    .card-header-custom {
        background: linear-gradient(135deg, #2c3e50, #34495e);
        color: white;
        border-radius: 8px 8px 0 0;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(52, 152, 219, 0.05);
    }
</style>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Out of Stock Products</h1>
            <p class="text-muted">Products that need to be restocked</p>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="dashboard.php" class="btn btn-outline-primary rounded-pill">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header card-header-custom py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-times-circle me-2"></i>
                    Out of Stock Products (<?php echo count($out_of_stock_products); ?>)
                </h5>
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="productSearch" class="form-control form-control-sm" placeholder="Search products...">
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($out_of_stock_products) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle product-table" id="productsTable">
                        <thead>
                            <tr>
                                <th class="text-center">Image</th>
                                <th>Product Name</th>
                                <th>Product Type</th>
                                <th>Price</th>
                                <th class="text-center">Alert Level</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($out_of_stock_products as $product): ?>
                                <tr>
                                    <td class="text-center" style="width: 80px;">
                                        <?php 
                                        // Determine image based on product name
                                        $imageName = '';
                                        $productNameLower = strtolower($product['product_name']);
                                        
                                        if (strpos($productNameLower, 'iphone 13') !== false || strpos($productNameLower, '13 pro') !== false) {
                                            $imageName = '13pro.jpg';
                                        } elseif (strpos($productNameLower, 'iphone 14') !== false || strpos($productNameLower, '14') !== false) {
                                            $imageName = '14pro.jpg';
                                        } elseif (strpos($productNameLower, 's22') !== false || strpos($productNameLower, 'galaxy s22') !== false) {
                                            $imageName = 's22.jpg';
                                        } elseif (strpos($productNameLower, 'fold') !== false || strpos($productNameLower, 'z fold') !== false) {
                                            $imageName = 'zfold4.jpg';
                                        } elseif (strpos($productNameLower, 'playstation') !== false || strpos($productNameLower, 'ps5') !== false) {
                                            $imageName = 'playstation5.jpg';
                                        }
                                        
                                        if (!empty($imageName)):
                                        ?>
                                            <img src="../images/products/<?php echo $imageName; ?>"
                                                alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                                                class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center"
                                                style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($product['product_name']); ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                        // Display product type based on product name
                                        if (stripos($product['product_name'], 'iphone') !== false) {
                                            echo '<span class="badge bg-primary">iPhone</span>';
                                        } elseif (stripos($product['product_name'], 'samsung') !== false) {
                                            echo '<span class="badge bg-info">Samsung</span>';
                                        } elseif (stripos($product['product_name'], 'playstation') !== false ||
                                                 stripos($product['product_name'], 'ps5') !== false) {
                                            echo '<span class="badge bg-dark">Gaming</span>';
                                        } else {
                                            echo '<span class="badge bg-secondary">Other</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo formatCurrency($product['price']); ?></td>
                                    <td class="text-center"><?php echo $product['alert_level']; ?></td>
                                    <td class="text-center">
                                        <span class="badge bg-danger">Out of Stock</span>
                                    </td>
                                    <td class="text-center">
                                        <a href="restock.php?id=<?php echo $product['product_id']; ?>" class="btn btn-sm btn-success">
                                            <i class="fas fa-plus-circle"></i> Restock
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5>No out of stock products</h5>
                    <p class="text-muted">All products are currently in stock.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('productSearch');
    const table = document.getElementById('productsTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    const noResultsMessage = document.createElement('tr');
    const tableBody = table.getElementsByTagName('tbody')[0];

    // Create "no results" message
    noResultsMessage.innerHTML = `
        <td colspan="7" class="text-center py-4">
            <i class="fas fa-search text-muted mb-3" style="font-size: 2rem;"></i>
            <p class="mb-0">No products match your search</p>
        </td>
    `;
    noResultsMessage.style.display = 'none';
    noResultsMessage.id = 'noResultsRow';
    tableBody.appendChild(noResultsMessage);

    // Function to filter table
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        let visibleCount = 0;

        for (let i = 0; i < rows.length; i++) {
            // Skip the no results row
            if (rows[i].id === 'noResultsRow') continue;

            const productName = rows[i].getElementsByTagName('td')[1].textContent.toLowerCase();
            const productType = rows[i].getElementsByTagName('td')[2].textContent.toLowerCase();
            const price = rows[i].getElementsByTagName('td')[3].textContent.toLowerCase();

            if (productName.includes(searchTerm) ||
                productType.includes(searchTerm) ||
                price.includes(searchTerm)) {
                rows[i].style.display = '';
                visibleCount++;
            } else {
                rows[i].style.display = 'none';
            }
        }

        // Show or hide the "no results" message
        if (visibleCount === 0 && searchTerm !== '') {
            document.getElementById('noResultsRow').style.display = 'table-row';
        } else {
            document.getElementById('noResultsRow').style.display = 'none';
        }
    }

    // Event listeners
    searchInput.addEventListener('keyup', filterTable);
    searchInput.addEventListener('search', filterTable); // For when the clear button is clicked

    // Add clear button functionality
    const clearSearch = document.createElement('span');
    clearSearch.innerHTML = '<i class="fas fa-times-circle"></i>';
    clearSearch.style.position = 'absolute';
    clearSearch.style.right = '10px';
    clearSearch.style.top = '10px';
    clearSearch.style.cursor = 'pointer';
    clearSearch.style.color = '#6c757d';
    clearSearch.style.display = 'none';

    searchInput.parentNode.appendChild(clearSearch);

    searchInput.addEventListener('input', function() {
        clearSearch.style.display = this.value ? 'block' : 'none';
    });

    clearSearch.addEventListener('click', function() {
        searchInput.value = '';
        filterTable();
        this.style.display = 'none';
        searchInput.focus();
    });
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
