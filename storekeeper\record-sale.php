<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize variables
$product_id = $quantity = $notes = "";
$product_id_err = $quantity_err = "";
$success_message = $error_message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate product
    if (empty($_POST["product_id"])) {
        $product_id_err = "Please select a product.";
    } else {
        $product_id = $_POST["product_id"];
    }

    // Validate quantity
    if (empty($_POST["quantity"])) {
        $quantity_err = "Please enter a quantity.";
    } elseif (!is_numeric($_POST["quantity"]) || $_POST["quantity"] <= 0) {
        $quantity_err = "Please enter a valid quantity.";
    } else {
        $quantity = $_POST["quantity"];

        // Check if quantity is available
        $stmt = $pdo->prepare("SELECT quantity FROM products WHERE product_id = ?");
        $stmt->execute([$product_id]);
        $current_quantity = $stmt->fetch()['quantity'];

        if ($quantity > $current_quantity) {
            $quantity_err = "Insufficient stock. Only " . $current_quantity . " available.";
        }
    }

    // Get notes
    $notes = trim($_POST["notes"]);

    // Check input errors before inserting into database
    if (empty($product_id_err) && empty($quantity_err)) {
        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Calculate new quantity
            $new_quantity = $current_quantity - $quantity;

            // Update product quantity
            $stmt = $pdo->prepare("UPDATE products SET quantity = ? WHERE product_id = ?");
            $stmt->execute([$new_quantity, $product_id]);

            // Log stock transaction - verify user_id exists first
            $user_id = $_SESSION['user_id'] ?? null;

            // Verify user exists in database
            if ($user_id) {
                $check_user = $pdo->prepare("SELECT user_id FROM users WHERE user_id = ?");
                $check_user->execute([$user_id]);

                if ($check_user->rowCount() > 0) {
                    // User exists, proceed with transaction
                    $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, 'out', ?, ?)");
                    $stmt->execute([$product_id, $user_id, $quantity, $notes]);
                } else {
                    // User doesn't exist, use a default admin user from the database
                    $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
                    $admin = $get_admin->fetch();

                    if ($admin && isset($admin['user_id'])) {
                        $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, 'out', ?, ?)");
                        $stmt->execute([$product_id, $admin['user_id'], $quantity, $notes . ' (system)']);
                    } else {
                        // No admin found, just update the product quantity without logging
                        error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
                    }
                }
            } else {
                // No user_id in session, use a default admin
                $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
                $admin = $get_admin->fetch();

                if ($admin && isset($admin['user_id'])) {
                    $stmt = $pdo->prepare("INSERT INTO stock_transactions (product_id, user_id, transaction_type, quantity, notes) VALUES (?, ?, 'out', ?, ?)");
                    $stmt->execute([$product_id, $admin['user_id'], $quantity, $notes . ' (system)']);
                } else {
                    // No admin found, just update the product quantity without logging
                    error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
                }
            }

            // Commit transaction
            $pdo->commit();

            // Set success message
            $success_message = "Sale recorded successfully!";

            // Clear form data
            $product_id = $quantity = $notes = "";
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

// Get all products with available stock for dropdown
$stmt = $pdo->query("SELECT * FROM products WHERE quantity > 0 ORDER BY product_name");
$products = $stmt->fetchAll();

// Get recent sales by this user
$stmt = $pdo->prepare("
    SELECT t.*, p.product_name, p.price
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    WHERE t.user_id = ? AND t.transaction_type = 'out'
    ORDER BY t.transaction_date DESC
    LIMIT 10
");
$stmt->execute([$_SESSION['user_id']]);
$recent_sales = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Record Sale</h1>
    <a href="dashboard.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
    </a>
</div>

<?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="row g-4">
    <div class="col-lg-5">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">Record New Sale</h5>
            </div>
            <div class="card-body">
                <?php if (count($products) > 0): ?>
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="product_id" class="form-label">Select Product</label>
                            <select name="product_id" id="product_id" class="form-select <?php echo (!empty($product_id_err)) ? 'is-invalid' : ''; ?>" required>
                                <option value="" selected disabled>-- Select Product --</option>
                                <?php foreach ($products as $product): ?>
                                    <option value="<?php echo $product['product_id']; ?>"
                                            data-price="<?php echo $product['price']; ?>"
                                            data-stock="<?php echo $product['quantity']; ?>"
                                            <?php echo ($product_id == $product['product_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($product['product_name']); ?> (<?php echo htmlspecialchars($product['category']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                <?php echo $product_id_err; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="current_stock" class="form-label">Available Stock</label>
                                <input type="text" id="current_stock" class="form-control" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="price" class="form-label">Unit Price</label>
                                <input type="text" id="price" class="form-control" readonly>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity Sold</label>
                            <input type="number" name="quantity" id="quantity" min="1" class="form-control <?php echo (!empty($quantity_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $quantity; ?>" required>
                            <div class="invalid-feedback">
                                <?php echo $quantity_err; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="total_price" class="form-label">Total Price</label>
                            <input type="text" id="total_price" class="form-control bg-light" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (Optional)</label>
                            <textarea name="notes" id="notes" class="form-control" rows="2"><?php echo $notes; ?></textarea>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-shopping-cart me-1"></i> Record Sale
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i> No products with available stock found. Please contact the administrator to add stock.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-7">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">Your Recent Sales</h5>
            </div>
            <div class="card-body">
                <?php if (count($recent_sales) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_sales as $sale): ?>
                                    <tr>
                                        <td><?php echo date('M d, Y H:i', strtotime($sale['transaction_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($sale['product_name']); ?></td>
                                        <td><?php echo $sale['quantity']; ?></td>
                                        <td><?php echo formatCurrency($sale['price']); ?></td>
                                        <td><?php echo formatCurrency($sale['quantity'] * $sale['price']); ?></td>
                                        <td><?php echo htmlspecialchars($sale['notes'] ?: '-'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <p class="mb-0">No sales recorded yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic product selection
    const productSelect = document.getElementById('product_id');
    const priceField = document.getElementById('price');
    const currentStockField = document.getElementById('current_stock');
    const quantityField = document.getElementById('quantity');
    const totalPriceField = document.getElementById('total_price');

    function updateTotalPrice() {
        if (priceField.value && quantityField.value) {
            const price = parseFloat(priceField.value.replace('₦', '').replace(/,/g, ''));
            const quantity = parseInt(quantityField.value);
            if (!isNaN(price) && !isNaN(quantity)) {
                totalPriceField.value = '₦' + (price * quantity).toLocaleString();
            }
        } else {
            totalPriceField.value = '';
        }
    }

    if (productSelect && priceField && currentStockField && quantityField && totalPriceField) {
        productSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.getAttribute('data-price');
            const stock = selectedOption.getAttribute('data-stock');

            priceField.value = price ? '₦' + parseFloat(price).toLocaleString() : '';
            currentStockField.value = stock || '0';

            // Set max quantity to available stock
            quantityField.max = stock;

            // Clear quantity field
            quantityField.value = '';
            totalPriceField.value = '';
        });

        // Trigger change event if a product is already selected
        if (productSelect.value) {
            productSelect.dispatchEvent(new Event('change'));
        }

        // Update total price when quantity changes
        quantityField.addEventListener('input', function() {
            updateTotalPrice();

            // Validate quantity against available stock
            const availableStock = parseInt(currentStockField.value);
            const enteredQuantity = parseInt(this.value);

            if (enteredQuantity > availableStock) {
                this.setCustomValidity(`Only ${availableStock} units available`);
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // Form validation
    const form = document.querySelector('.needs-validation');

    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        }, false);
    }
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
