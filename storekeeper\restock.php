<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize variables
$product = null;
$success_message = '';
$error_message = '';

// Check if product ID is provided
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $product_id = $_GET['id'];

    // Get product details
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();

    // If product not found, redirect to out-of-stock page
    if (!$product) {
        header('Location: out-of-stock.php');
        exit;
    }
} else {
    // Redirect to out-of-stock page if no ID provided
    header('Location: out-of-stock.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 0;
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';

    if ($quantity <= 0) {
        $error_message = 'Please enter a valid quantity greater than zero.';
    } else {
        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Update product quantity
            $stmt = $pdo->prepare("UPDATE products SET quantity = quantity + ? WHERE product_id = ?");
            $stmt->execute([$quantity, $product_id]);

            // Record stock transaction - verify user_id exists first
            $user_id = $_SESSION['user_id'] ?? null;

            // Verify user exists in database
            if ($user_id) {
                $check_user = $pdo->prepare("SELECT user_id FROM users WHERE user_id = ?");
                $check_user->execute([$user_id]);

                if ($check_user->rowCount() > 0) {
                    // User exists, proceed with transaction
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_transactions (product_id, user_id, quantity, transaction_type, notes, transaction_date)
                        VALUES (?, ?, ?, 'in', ?, NOW())
                    ");
                    $stmt->execute([$product_id, $user_id, $quantity, $notes]);
                } else {
                    // User doesn't exist, use a default admin user from the database
                    $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
                    $admin = $get_admin->fetch();

                    if ($admin && isset($admin['user_id'])) {
                        $stmt = $pdo->prepare("
                            INSERT INTO stock_transactions (product_id, user_id, quantity, transaction_type, notes, transaction_date)
                            VALUES (?, ?, ?, 'in', ?, NOW())
                        ");
                        $stmt->execute([$product_id, $admin['user_id'], $quantity, $notes . ' (system)']);
                    } else {
                        // No admin found, just update the product quantity without logging
                        error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
                    }
                }
            } else {
                // No user_id in session, use a default admin
                $get_admin = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
                $admin = $get_admin->fetch();

                if ($admin && isset($admin['user_id'])) {
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_transactions (product_id, user_id, quantity, transaction_type, notes, transaction_date)
                        VALUES (?, ?, ?, 'in', ?, NOW())
                    ");
                    $stmt->execute([$product_id, $admin['user_id'], $quantity, $notes . ' (system)']);
                } else {
                    // No admin found, just update the product quantity without logging
                    error_log("Warning: No valid user found for stock transaction. Stock updated without transaction log.");
                }
            }

            // Commit transaction
            $pdo->commit();

            $success_message = 'Product has been restocked successfully.';

            // Refresh product data
            $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ?");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch();

        } catch (Exception $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error_message = 'An error occurred while restocking the product: ' . $e->getMessage();
        }
    }
}

// Include header
include '../includes/header.php';
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Restock Product</h1>
            <p class="text-muted">Add inventory to out of stock product</p>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="out-of-stock.php" class="btn btn-outline-primary rounded-pill">
                <i class="fas fa-arrow-left me-2"></i>Back to Out of Stock Products
            </a>
        </div>
    </div>

    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header py-3" style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white;">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Restock Product</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productName" class="form-label">Product Name</label>
                                    <input type="text" class="form-control" id="productName" value="<?php echo htmlspecialchars($product['product_name']); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="currentStock" class="form-label">Current Stock</label>
                                    <input type="text" class="form-control" id="currentStock" value="<?php echo $product['quantity']; ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="alertLevel" class="form-label">Alert Level</label>
                                    <input type="text" class="form-control" id="alertLevel" value="<?php echo $product['alert_level']; ?>" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price</label>
                                    <input type="text" class="form-control" id="price" value="<?php echo formatCurrency($product['price']); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">Quantity to Add <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Enter any notes about this restock (optional)"></textarea>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="out-of-stock.php" class="btn btn-outline-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus-circle me-2"></i>Restock Product
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header py-3" style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white;">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Product Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <?php
                        // Determine image based on product name
                        $imageName = '';
                        $productNameLower = strtolower($product['product_name']);

                        if (strpos($productNameLower, 'iphone 13') !== false || strpos($productNameLower, '13 pro') !== false) {
                            $imageName = '13pro.jpg';
                        } elseif (strpos($productNameLower, 'iphone 14') !== false || strpos($productNameLower, '14') !== false) {
                            $imageName = '14pro.jpg';
                        } elseif (strpos($productNameLower, 's22') !== false || strpos($productNameLower, 'galaxy s22') !== false) {
                            $imageName = 's22.jpg';
                        } elseif (strpos($productNameLower, 'fold') !== false || strpos($productNameLower, 'z fold') !== false) {
                            $imageName = 'zfold4.jpg';
                        } elseif (strpos($productNameLower, 'playstation') !== false || strpos($productNameLower, 'ps5') !== false) {
                            $imageName = 'playstation5.jpg';
                        }

                        if (!empty($imageName)):
                        ?>
                            <img src="../images/products/<?php echo $imageName; ?>"
                                alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                                class="img-fluid rounded" style="max-height: 200px;">
                        <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center rounded"
                                style="height: 200px; width: 100%;">
                                <i class="fas fa-image text-muted fa-4x"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="alert alert-danger mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">Out of Stock</h6>
                                <p class="mb-0 small">This product is currently out of stock and needs to be restocked.</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Product Details</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span>Product ID</span>
                                <span class="badge bg-secondary rounded-pill"><?php echo $product['product_id']; ?></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span>Status</span>
                                <span class="badge bg-danger">Out of Stock</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span>Last Updated</span>
                                <span><?php echo date('M d, Y', strtotime($product['last_updated'])); ?></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include '../includes/footer.php';
?>
