<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Set default date range to last 7 days
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$product_id = isset($_GET['product_id']) ? $_GET['product_id'] : '';

// Build query based on filters
$query = "
    SELECT t.*, p.product_name, p.category, p.price
    FROM stock_transactions t
    JOIN products p ON t.product_id = p.product_id
    WHERE t.user_id = ? AND t.transaction_type = 'out'
    AND DATE(t.transaction_date) BETWEEN ? AND ?
";
$params = [$_SESSION['user_id'], $start_date, $end_date];

if (!empty($product_id)) {
    $query .= " AND t.product_id = ?";
    $params[] = $product_id;
}

$query .= " ORDER BY t.transaction_date DESC";

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$sales = $stmt->fetchAll();

// Calculate totals
$total_items_sold = 0;
$total_sales_value = 0;

foreach ($sales as $sale) {
    $total_items_sold += $sale['quantity'];
    $total_sales_value += $sale['quantity'] * $sale['price'];
}

// Get products for filter
$stmt = $pdo->prepare("
    SELECT DISTINCT p.product_id, p.product_name
    FROM products p
    JOIN stock_transactions t ON p.product_id = t.product_id
    WHERE t.user_id = ? AND t.transaction_type = 'out'
    ORDER BY p.product_name
");
$stmt->execute([$_SESSION['user_id']]);
$products = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Sales History</h1>
    <div>
        <a href="record-sale.php" class="btn btn-primary me-2">
            <i class="fas fa-shopping-cart me-1"></i> Record Sale
        </a>
        <button id="printReport" class="btn btn-secondary">
            <i class="fas fa-print me-1"></i> Print
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4 no-print">
    <div class="card-body">
        <form action="" method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" id="start_date" name="start_date" class="form-control" value="<?php echo $start_date; ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" id="end_date" name="end_date" class="form-control" value="<?php echo $end_date; ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-5">
                <label for="product_id" class="form-label">Product</label>
                <select name="product_id" id="product_id" class="form-select">
                    <option value="">All Products</option>
                    <?php foreach ($products as $product): ?>
                        <option value="<?php echo $product['product_id']; ?>" <?php echo ($product_id == $product['product_id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($product['product_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Report Header -->
<div class="card border-0 shadow-sm mb-4 print-section">
    <div class="card-body">
        <div class="text-center mb-4">
            <h4 class="mb-0">Griffin Gadgets</h4>
            <p class="mb-0">Sales History Report</p>
            <p class="text-muted">
                <?php 
                if ($start_date == $end_date) {
                    echo date('F d, Y', strtotime($start_date));
                } else {
                    echo date('F d, Y', strtotime($start_date)) . ' - ' . date('F d, Y', strtotime($end_date));
                }
                ?>
            </p>
            <p class="text-muted">Generated by: <?php echo htmlspecialchars($_SESSION['full_name']); ?></p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Sales</h5>
                        <p class="display-6 mb-0"><?php echo formatCurrency($total_sales_value); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Items Sold</h5>
                        <p class="display-6 mb-0"><?php echo $total_items_sold; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Table -->
<div class="card border-0 shadow-sm print-section">
    <div class="card-header bg-white">
        <h5 class="mb-0">Sales Details</h5>
    </div>
    <div class="card-body">
        <?php if (count($sales) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date & Time</th>
                            <th>Product</th>
                            <th>Category</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total</th>
                            <th class="no-print">Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sales as $sale): ?>
                            <tr>
                                <td><?php echo date('M d, Y H:i', strtotime($sale['transaction_date'])); ?></td>
                                <td><?php echo htmlspecialchars($sale['product_name']); ?></td>
                                <td><?php echo htmlspecialchars($sale['category']); ?></td>
                                <td><?php echo $sale['quantity']; ?></td>
                                <td><?php echo formatCurrency($sale['price']); ?></td>
                                <td><?php echo formatCurrency($sale['quantity'] * $sale['price']); ?></td>
                                <td class="no-print"><?php echo htmlspecialchars($sale['notes'] ?: '-'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-active">
                            <th colspan="3">Total</th>
                            <th><?php echo $total_items_sold; ?></th>
                            <th></th>
                            <th><?php echo formatCurrency($total_sales_value); ?></th>
                            <th class="no-print"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <p class="mb-0">No sales found for the selected date range.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .navbar, .footer, .btn {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }
    
    .print-section {
        page-break-inside: avoid;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Print report
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });
    
    // Date validation
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    endDateInput.addEventListener('change', function() {
        if (startDateInput.value && this.value && this.value < startDateInput.value) {
            alert('End date cannot be earlier than start date');
            this.value = startDateInput.value;
        }
    });
    
    startDateInput.addEventListener('change', function() {
        if (endDateInput.value && this.value && this.value > endDateInput.value) {
            endDateInput.value = this.value;
        }
    });
});
</script>

<?php
// Include footer
include '../includes/footer.php';
?>
