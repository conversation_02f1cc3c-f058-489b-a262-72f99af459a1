<?php
// Set storekeeper flag
$is_storekeeper = true;

// Include functions file
require_once '../includes/functions.php';

// Check if user is storekeeper
requireStorekeeper();

// Include database connection
require_once '../includes/db_connect.php';

// Initialize variables
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$report_data = [];
$total_opening_value = 0;
$total_closing_value = 0;
$total_received_value = 0;
$total_sold_value = 0;

// Generate report if dates are set
if ($start_date && $end_date) {
    $report_data = getStockBookReport($pdo, $start_date, $end_date);

    // Calculate totals
    foreach ($report_data as $item) {
        $total_opening_value += $item['opening_stock'] * $item['price'];
        $total_closing_value += $item['closing_value'];
        $total_received_value += $item['received'] * $item['price'];
        $total_sold_value += $item['sold'] * $item['price'];
    }
}

// Include header
include '../includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Stock Book Report</h1>
    <button id="printReport" class="btn btn-primary">
        <i class="fas fa-print me-1"></i> Print Report
    </button>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-body p-4">
        <form method="get" action="stock-book.php" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i> Generate Report
                </button>
            </div>
        </form>
    </div>
</div>

<div id="reportContent">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">Stock Book Report</h5>
                    <p class="text-muted mb-0">Period: <?php echo date('d M, Y', strtotime($start_date)); ?> - <?php echo date('d M, Y', strtotime($end_date)); ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5 class="mb-0">Griffin Gadgets</h5>
                    <p class="text-muted mb-0">37a St. Michaels Aba</p>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th class="text-end">Price (₦)</th>
                            <th class="text-end">Opening Stock</th>
                            <th class="text-end">Received</th>
                            <th class="text-end">Sold</th>
                            <th class="text-end">Closing Stock</th>
                            <th class="text-end">Closing Value (₦)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($report_data) > 0): ?>
                            <?php foreach ($report_data as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['category']); ?></td>
                                    <td class="text-end"><?php echo number_format($item['price'], 0); ?></td>
                                    <td class="text-end"><?php echo $item['opening_stock']; ?></td>
                                    <td class="text-end"><?php echo $item['received']; ?></td>
                                    <td class="text-end"><?php echo $item['sold']; ?></td>
                                    <td class="text-end"><?php echo $item['closing_stock']; ?></td>
                                    <td class="text-end"><?php echo number_format($item['closing_value'], 0); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">No data available for the selected period.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th colspan="3">TOTALS</th>
                            <th class="text-end">-</th>
                            <th class="text-end">-</th>
                            <th class="text-end"><?php echo number_format($total_sold_value, 0); ?> ₦</th>
                            <th colspan="2" class="text-end"><?php echo number_format($total_closing_value, 0); ?> ₦</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4 text-center d-none d-print-block">
        <p>Report generated on <?php echo date('d M, Y H:i'); ?></p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Print functionality
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });

    // Validate date range
    document.querySelector('form').addEventListener('submit', function(e) {
        const startDate = new Date(document.getElementById('start_date').value);
        const endDate = new Date(document.getElementById('end_date').value);

        if (startDate > endDate) {
            e.preventDefault();
            alert('Start date cannot be after end date');
        }
    });
});
</script>

<style>
@media print {
    body * {
        visibility: hidden;
    }
    #reportContent, #reportContent * {
        visibility: visible;
    }
    #reportContent {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table {
        width: 100%;
    }
}
</style>

<?php
// Include footer
include '../includes/footer.php';
?>
