<?php
// <PERSON><PERSON><PERSON> to update the database schema

// Include database connection
require_once 'includes/db_connect.php';

echo "<h1>Database Update</h1>";

// Check if the products table has the image_path column
echo "<h2>Checking Products Table Schema</h2>";
try {
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasImagePath = false;
    $hasCategory = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] == 'image_path') {
            $hasImagePath = true;
        }
        
        if ($column['Field'] == 'category') {
            $hasCategory = true;
        }
    }
    
    // Add image_path column if it doesn't exist
    if (!$hasImagePath) {
        echo "<p>Adding 'image_path' column to products table...</p>";
        $pdo->exec("ALTER TABLE products ADD COLUMN image_path VARCHAR(255) DEFAULT NULL");
        echo "<p style='color: green;'>Column 'image_path' added successfully!</p>";
    } else {
        echo "<p>Column 'image_path' already exists.</p>";
    }
    
    // Add category column if it doesn't exist
    if (!$hasCategory) {
        echo "<p>Adding 'category' column to products table...</p>";
        $pdo->exec("ALTER TABLE products ADD COLUMN category VARCHAR(50) DEFAULT 'General'");
        echo "<p style='color: green;'>Column 'category' added successfully!</p>";
    } else {
        echo "<p>Column 'category' already exists.</p>";
    }
    
    // Update existing products with image paths
    echo "<h2>Updating Product Image Paths</h2>";
    
    $productImages = [
        'iPhone 13 Pro' => 'images/products/13pro.jpg',
        'iPhone 14' => 'images/products/14pro.jpg',
        'Samsung Galaxy S22' => 'images/products/s22.jpg',
        'Samsung Galaxy Z Fold 4' => 'images/products/zfold4.jpg',
        'PlayStation 5' => 'images/products/playstation5.jpg'
    ];
    
    $productCategories = [
        'iPhone 13 Pro' => 'Phones',
        'iPhone 14' => 'Phones',
        'Samsung Galaxy S22' => 'Phones',
        'Samsung Galaxy Z Fold 4' => 'Phones',
        'PlayStation 5' => 'Gaming'
    ];
    
    foreach ($productImages as $productName => $imagePath) {
        $category = $productCategories[$productName] ?? 'General';
        
        $stmt = $pdo->prepare("UPDATE products SET image_path = ?, category = ? WHERE product_name = ? AND (image_path IS NULL OR image_path = '')");
        $stmt->execute([$imagePath, $category, $productName]);
        
        $rowCount = $stmt->rowCount();
        echo "<p>Updated " . $productName . ": " . $rowCount . " row(s) affected.</p>";
    }
    
    echo "<h2>Current Products</h2>";
    $stmt = $pdo->query("SELECT product_id, product_name, category, image_path FROM products");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($products) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Product Name</th><th>Category</th><th>Image Path</th><th>Preview</th></tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . $product['product_id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['product_name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['category']) . "</td>";
            echo "<td>" . htmlspecialchars($product['image_path'] ?? 'No image') . "</td>";
            echo "<td>";
            if (!empty($product['image_path'])) {
                echo "<img src='" . htmlspecialchars($product['image_path']) . "' style='max-width: 100px; max-height: 100px;'>";
            } else {
                echo "No image";
            }
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No products found in the database.</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>Error: " . $e->getMessage() . "</div>";
}

// Add a link to go back to the admin page
echo "<p><a href='admin/products.php'>Go to Products Management</a></p>";
echo "<p><a href='index.php'>Go to Home Page</a></p>";
?>
