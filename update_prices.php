<?php
// Include database connection
require_once 'includes/db_connect.php';

// Define the product prices
$prices = [
    'iPhone 13 Pro' => 750000,
    'iPhone 14' => 650000,
    'Samsung Galaxy S22' => 550000,
    'Samsung Galaxy Z Fold 4' => 1200000,
    'PlayStation 5' => 450000
];

// Update each product price
foreach ($prices as $product_name => $price) {
    $stmt = $pdo->prepare('UPDATE products SET price = ? WHERE product_name = ?');
    $result = $stmt->execute([$price, $product_name]);
    echo $product_name . ' price updated to ' . $price . ': ' . ($result ? 'Success' : 'Failed') . '<br>';
}

echo '<p>Price update completed. <a href="index.php">Return to homepage</a></p>';
?>
