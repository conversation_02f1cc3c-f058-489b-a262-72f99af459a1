<?php
// Files that should have been removed
$filesToCheck = [
    'check-database.php',
    'check-image.php',
    'check-image-directory.php',
    'copy-image.php',
    'debug-image.php',
    'image-upload-test.php',
    'generate-placeholder.php',
    'db_setup.php',
    'check-file-usage.php',
    'check-files-exist.php',
    'check-database-dir.php',
    'list-files.php',
    'database/db_setup.sql'
];

echo "<h1>Verifying File Removal</h1>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>File</th><th>Still Exists</th></tr>";

$allRemoved = true;

foreach ($filesToCheck as $file) {
    $exists = file_exists($file);
    
    if ($exists) {
        $allRemoved = false;
    }
    
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td>" . ($exists ? "Yes (Not Removed)" : "No (Successfully Removed)") . "</td>";
    echo "</tr>";
}

echo "</table>";

if ($allRemoved) {
    echo "<p>All unnecessary files have been successfully removed.</p>";
} else {
    echo "<p>Some files could not be removed. They may be in use or require higher permissions.</p>";
}

// Check if this script exists (it should)
echo "<p>This script (verify-removal.php) " . (file_exists('verify-removal.php') ? "exists" : "does not exist") . " and will be removed after verification.</p>";

// Remove this script too
echo "<p>Removing this script: " . (unlink('verify-removal.php') ? "Success" : "Failed") . "</p>";
?>
